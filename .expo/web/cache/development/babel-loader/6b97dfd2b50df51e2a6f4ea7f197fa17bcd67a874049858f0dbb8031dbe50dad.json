{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"activeOpacity\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"rejectResponderTermination\", \"style\"];\nimport * as React from 'react';\nimport { useCallback, useMemo, useState, useRef } from 'react';\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nimport usePressEvents from \"../../modules/usePressEvents\";\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nfunction TouchableOpacity(props, forwardedRef) {\n  var activeOpacity = props.activeOpacity,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = useRef(null);\n  var setRef = useMergeRefs(forwardedRef, hostRef);\n  var _useState = useState('0s'),\n    duration = _useState[0],\n    setDuration = _useState[1];\n  var _useState2 = useState(null),\n    opacityOverride = _useState2[0],\n    setOpacityOverride = _useState2[1];\n  var setOpacityTo = useCallback(function (value, duration) {\n    setOpacityOverride(value);\n    setDuration(duration ? duration / 1000 + \"s\" : '0s');\n  }, [setOpacityOverride, setDuration]);\n  var setOpacityActive = useCallback(function (duration) {\n    setOpacityTo(activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.2, duration);\n  }, [activeOpacity, setOpacityTo]);\n  var setOpacityInactive = useCallback(function (duration) {\n    setOpacityTo(null, duration);\n  }, [setOpacityTo]);\n  var pressConfig = useMemo(function () {\n    return {\n      cancelable: !rejectResponderTermination,\n      disabled: disabled,\n      delayLongPress: delayLongPress,\n      delayPressStart: delayPressIn,\n      delayPressEnd: delayPressOut,\n      onLongPress: onLongPress,\n      onPress: onPress,\n      onPressStart: function onPressStart(event) {\n        var isGrant = event.dispatchConfig != null ? event.dispatchConfig.registrationName === 'onResponderGrant' : event.type === 'keydown';\n        setOpacityActive(isGrant ? 0 : 150);\n        if (onPressIn != null) {\n          onPressIn(event);\n        }\n      },\n      onPressEnd: function onPressEnd(event) {\n        setOpacityInactive(250);\n        if (onPressOut != null) {\n          onPressOut(event);\n        }\n      }\n    };\n  }, [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, setOpacityActive, setOpacityInactive]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  return React.createElement(View, _extends({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, !disabled && styles.actionable, style, opacityOverride != null && {\n      opacity: opacityOverride\n    }, {\n      transitionDuration: duration\n    }]\n  }));\n}\nvar styles = StyleSheet.create({\n  root: {\n    transitionProperty: 'opacity',\n    transitionDuration: '0.15s',\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableOpacity = React.memo(React.forwardRef(TouchableOpacity));\nMemoedTouchableOpacity.displayName = 'TouchableOpacity';\nexport default MemoedTouchableOpacity;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useCallback", "useMemo", "useState", "useRef", "useMergeRefs", "usePressEvents", "StyleSheet", "View", "TouchableOpacity", "props", "forwardedRef", "activeOpacity", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onLongPress", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "style", "rest", "hostRef", "setRef", "_useState", "duration", "setDuration", "_useState2", "opacityOverride", "setOpacityOverride", "setOpacityTo", "value", "setOpacityActive", "setOpacityInactive", "pressConfig", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "event", "isGrant", "dispatchConfig", "registrationName", "type", "onPressEnd", "pressEventHandlers", "createElement", "accessibilityDisabled", "pointerEvents", "undefined", "ref", "styles", "root", "actionable", "opacity", "transitionDuration", "create", "transitionProperty", "userSelect", "cursor", "touchAction", "MemoedTouchableOpacity", "memo", "forwardRef", "displayName"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/TouchableOpacity/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"activeOpacity\", \"delayPressIn\", \"delayPressOut\", \"delayLongPress\", \"disabled\", \"focusable\", \"onLongPress\", \"onPress\", \"onPressIn\", \"onPressOut\", \"rejectResponderTermination\", \"style\"];\nimport * as React from 'react';\nimport { useCallback, useMemo, useState, useRef } from 'react';\nimport useMergeRefs from '../../modules/useMergeRefs';\nimport usePressEvents from '../../modules/usePressEvents';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\n//import { warnOnce } from '../../modules/warnOnce';\n\n/**\n * A wrapper for making views respond properly to touches.\n * On press down, the opacity of the wrapped view is decreased, dimming it.\n */\nfunction TouchableOpacity(props, forwardedRef) {\n  /*\n  warnOnce(\n    'TouchableOpacity',\n    'TouchableOpacity is deprecated. Please use Pressable.'\n  );\n  */\n\n  var activeOpacity = props.activeOpacity,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var hostRef = useRef(null);\n  var setRef = useMergeRefs(forwardedRef, hostRef);\n  var _useState = useState('0s'),\n    duration = _useState[0],\n    setDuration = _useState[1];\n  var _useState2 = useState(null),\n    opacityOverride = _useState2[0],\n    setOpacityOverride = _useState2[1];\n  var setOpacityTo = useCallback((value, duration) => {\n    setOpacityOverride(value);\n    setDuration(duration ? duration / 1000 + \"s\" : '0s');\n  }, [setOpacityOverride, setDuration]);\n  var setOpacityActive = useCallback(duration => {\n    setOpacityTo(activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.2, duration);\n  }, [activeOpacity, setOpacityTo]);\n  var setOpacityInactive = useCallback(duration => {\n    setOpacityTo(null, duration);\n  }, [setOpacityTo]);\n  var pressConfig = useMemo(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart(event) {\n      var isGrant = event.dispatchConfig != null ? event.dispatchConfig.registrationName === 'onResponderGrant' : event.type === 'keydown';\n      setOpacityActive(isGrant ? 0 : 150);\n      if (onPressIn != null) {\n        onPressIn(event);\n      }\n    },\n    onPressEnd(event) {\n      setOpacityInactive(250);\n      if (onPressOut != null) {\n        onPressOut(event);\n      }\n    }\n  }), [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, setOpacityActive, setOpacityInactive]);\n  var pressEventHandlers = usePressEvents(hostRef, pressConfig);\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, pressEventHandlers, {\n    accessibilityDisabled: disabled,\n    focusable: !disabled && focusable !== false,\n    pointerEvents: disabled ? 'box-none' : undefined,\n    ref: setRef,\n    style: [styles.root, !disabled && styles.actionable, style, opacityOverride != null && {\n      opacity: opacityOverride\n    }, {\n      transitionDuration: duration\n    }]\n  }));\n}\nvar styles = StyleSheet.create({\n  root: {\n    transitionProperty: 'opacity',\n    transitionDuration: '0.15s',\n    userSelect: 'none'\n  },\n  actionable: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  }\n});\nvar MemoedTouchableOpacity = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableOpacity));\nMemoedTouchableOpacity.displayName = 'TouchableOpacity';\nexport default MemoedTouchableOpacity;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,4BAA4B,EAAE,OAAO,CAAC;AACzM,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC9D,OAAOC,YAAY;AACnB,OAAOC,cAAc;AACrB,OAAOC,UAAU;AACjB,OAAOC,IAAI;AAOX,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAQ7C,IAAIC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACrCC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,WAAW,GAAGR,KAAK,CAACQ,WAAW;IAC/BC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,0BAA0B,GAAGZ,KAAK,CAACY,0BAA0B;IAC7DC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,IAAI,GAAG1B,6BAA6B,CAACY,KAAK,EAAEX,SAAS,CAAC;EACxD,IAAI0B,OAAO,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAIsB,MAAM,GAAGrB,YAAY,CAACM,YAAY,EAAEc,OAAO,CAAC;EAChD,IAAIE,SAAS,GAAGxB,QAAQ,CAAC,IAAI,CAAC;IAC5ByB,QAAQ,GAAGD,SAAS,CAAC,CAAC,CAAC;IACvBE,WAAW,GAAGF,SAAS,CAAC,CAAC,CAAC;EAC5B,IAAIG,UAAU,GAAG3B,QAAQ,CAAC,IAAI,CAAC;IAC7B4B,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACpC,IAAIG,YAAY,GAAGhC,WAAW,CAAC,UAACiC,KAAK,EAAEN,QAAQ,EAAK;IAClDI,kBAAkB,CAACE,KAAK,CAAC;IACzBL,WAAW,CAACD,QAAQ,GAAGA,QAAQ,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;EACtD,CAAC,EAAE,CAACI,kBAAkB,EAAEH,WAAW,CAAC,CAAC;EACrC,IAAIM,gBAAgB,GAAGlC,WAAW,CAAC,UAAA2B,QAAQ,EAAI;IAC7CK,YAAY,CAACrB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,GAAG,EAAEgB,QAAQ,CAAC;EAClG,CAAC,EAAE,CAAChB,aAAa,EAAEqB,YAAY,CAAC,CAAC;EACjC,IAAIG,kBAAkB,GAAGnC,WAAW,CAAC,UAAA2B,QAAQ,EAAI;IAC/CK,YAAY,CAAC,IAAI,EAAEL,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;EAClB,IAAII,WAAW,GAAGnC,OAAO,CAAC;IAAA,OAAO;MAC/BoC,UAAU,EAAE,CAAChB,0BAA0B;MACvCN,QAAQ,EAARA,QAAQ;MACRD,cAAc,EAAdA,cAAc;MACdwB,eAAe,EAAE1B,YAAY;MAC7B2B,aAAa,EAAE1B,aAAa;MAC5BI,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACPsB,YAAY,WAAZA,YAAYA,CAACC,KAAK,EAAE;QAClB,IAAIC,OAAO,GAAGD,KAAK,CAACE,cAAc,IAAI,IAAI,GAAGF,KAAK,CAACE,cAAc,CAACC,gBAAgB,KAAK,kBAAkB,GAAGH,KAAK,CAACI,IAAI,KAAK,SAAS;QACpIX,gBAAgB,CAACQ,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC;QACnC,IAAIvB,SAAS,IAAI,IAAI,EAAE;UACrBA,SAAS,CAACsB,KAAK,CAAC;QAClB;MACF,CAAC;MACDK,UAAU,WAAVA,UAAUA,CAACL,KAAK,EAAE;QAChBN,kBAAkB,CAAC,GAAG,CAAC;QACvB,IAAIf,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAACqB,KAAK,CAAC;QACnB;MACF;IACF,CAAC;EAAA,CAAC,EAAE,CAAC3B,cAAc,EAAEF,YAAY,EAAEC,aAAa,EAAEE,QAAQ,EAAEE,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,0BAA0B,EAAEa,gBAAgB,EAAEC,kBAAkB,CAAC,CAAC;EAC3K,IAAIY,kBAAkB,GAAG1C,cAAc,CAACmB,OAAO,EAAEY,WAAW,CAAC;EAC7D,OAAoBrC,KAAK,CAACiD,aAAa,CAACzC,IAAI,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAE2B,IAAI,EAAEwB,kBAAkB,EAAE;IACnFE,qBAAqB,EAAElC,QAAQ;IAC/BC,SAAS,EAAE,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;IAC3CkC,aAAa,EAAEnC,QAAQ,GAAG,UAAU,GAAGoC,SAAS;IAChDC,GAAG,EAAE3B,MAAM;IACXH,KAAK,EAAE,CAAC+B,MAAM,CAACC,IAAI,EAAE,CAACvC,QAAQ,IAAIsC,MAAM,CAACE,UAAU,EAAEjC,KAAK,EAAEQ,eAAe,IAAI,IAAI,IAAI;MACrF0B,OAAO,EAAE1B;IACX,CAAC,EAAE;MACD2B,kBAAkB,EAAE9B;IACtB,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACA,IAAI0B,MAAM,GAAG/C,UAAU,CAACoD,MAAM,CAAC;EAC7BJ,IAAI,EAAE;IACJK,kBAAkB,EAAE,SAAS;IAC7BF,kBAAkB,EAAE,OAAO;IAC3BG,UAAU,EAAE;EACd,CAAC;EACDL,UAAU,EAAE;IACVM,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,IAAIC,sBAAsB,GAAgBhE,KAAK,CAACiE,IAAI,CAAcjE,KAAK,CAACkE,UAAU,CAACzD,gBAAgB,CAAC,CAAC;AACrGuD,sBAAsB,CAACG,WAAW,GAAG,kBAAkB;AACvD,eAAeH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}