{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nvar NativeAnimatedAPI = NativeAnimatedHelper.API;\nimport invariant from 'fbjs/lib/invariant';\nvar _uniqueId = 1;\nvar AnimatedNode = function () {\n  function AnimatedNode() {\n    _classCallCheck(this, AnimatedNode);\n    this._listeners = {};\n  }\n  return _createClass(AnimatedNode, [{\n    key: \"__attach\",\n    value: function __attach() {}\n  }, {\n    key: \"__detach\",\n    value: function __detach() {\n      if (this.__isNative && this.__nativeTag != null) {\n        NativeAnimatedHelper.API.dropAnimatedNode(this.__nativeTag);\n        this.__nativeTag = undefined;\n      }\n    }\n  }, {\n    key: \"__getValue\",\n    value: function __getValue() {}\n  }, {\n    key: \"__getAnimatedValue\",\n    value: function __getAnimatedValue() {\n      return this.__getValue();\n    }\n  }, {\n    key: \"__addChild\",\n    value: function __addChild(child) {}\n  }, {\n    key: \"__removeChild\",\n    value: function __removeChild(child) {}\n  }, {\n    key: \"__getChildren\",\n    value: function __getChildren() {\n      return [];\n    }\n  }, {\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      if (!this.__isNative) {\n        throw new Error('This node cannot be made a \"native\" animated node');\n      }\n      this._platformConfig = platformConfig;\n      if (this.hasListeners()) {\n        this._startListeningToNativeValueUpdates();\n      }\n    }\n  }, {\n    key: \"addListener\",\n    value: function addListener(callback) {\n      var id = String(_uniqueId++);\n      this._listeners[id] = callback;\n      if (this.__isNative) {\n        this._startListeningToNativeValueUpdates();\n      }\n      return id;\n    }\n  }, {\n    key: \"removeListener\",\n    value: function removeListener(id) {\n      delete this._listeners[id];\n      if (this.__isNative && !this.hasListeners()) {\n        this._stopListeningForNativeValueUpdates();\n      }\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners() {\n      this._listeners = {};\n      if (this.__isNative) {\n        this._stopListeningForNativeValueUpdates();\n      }\n    }\n  }, {\n    key: \"hasListeners\",\n    value: function hasListeners() {\n      return !!Object.keys(this._listeners).length;\n    }\n  }, {\n    key: \"_startListeningToNativeValueUpdates\",\n    value: function _startListeningToNativeValueUpdates() {\n      var _this = this;\n      if (this.__nativeAnimatedValueListener && !this.__shouldUpdateListenersForNewNativeTag) {\n        return;\n      }\n      if (this.__shouldUpdateListenersForNewNativeTag) {\n        this.__shouldUpdateListenersForNewNativeTag = false;\n        this._stopListeningForNativeValueUpdates();\n      }\n      NativeAnimatedAPI.startListeningToAnimatedNodeValue(this.__getNativeTag());\n      this.__nativeAnimatedValueListener = NativeAnimatedHelper.nativeEventEmitter.addListener('onAnimatedValueUpdate', function (data) {\n        if (data.tag !== _this.__getNativeTag()) {\n          return;\n        }\n        _this.__onAnimatedValueUpdateReceived(data.value);\n      });\n    }\n  }, {\n    key: \"__onAnimatedValueUpdateReceived\",\n    value: function __onAnimatedValueUpdateReceived(value) {\n      this.__callListeners(value);\n    }\n  }, {\n    key: \"__callListeners\",\n    value: function __callListeners(value) {\n      for (var _key in this._listeners) {\n        this._listeners[_key]({\n          value: value\n        });\n      }\n    }\n  }, {\n    key: \"_stopListeningForNativeValueUpdates\",\n    value: function _stopListeningForNativeValueUpdates() {\n      if (!this.__nativeAnimatedValueListener) {\n        return;\n      }\n      this.__nativeAnimatedValueListener.remove();\n      this.__nativeAnimatedValueListener = null;\n      NativeAnimatedAPI.stopListeningToAnimatedNodeValue(this.__getNativeTag());\n    }\n  }, {\n    key: \"__getNativeTag\",\n    value: function __getNativeTag() {\n      var _this$__nativeTag;\n      NativeAnimatedHelper.assertNativeAnimatedModule();\n      invariant(this.__isNative, 'Attempt to get native tag from node not marked as \"native\"');\n      var nativeTag = (_this$__nativeTag = this.__nativeTag) !== null && _this$__nativeTag !== void 0 ? _this$__nativeTag : NativeAnimatedHelper.generateNewNodeTag();\n      if (this.__nativeTag == null) {\n        this.__nativeTag = nativeTag;\n        var config = this.__getNativeConfig();\n        if (this._platformConfig) {\n          config.platformConfig = this._platformConfig;\n        }\n        NativeAnimatedHelper.API.createAnimatedNode(nativeTag, config);\n        this.__shouldUpdateListenersForNewNativeTag = true;\n      }\n      return nativeTag;\n    }\n  }, {\n    key: \"__getNativeConfig\",\n    value: function __getNativeConfig() {\n      throw new Error('This JS animated node type cannot be used as native animated node');\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return this.__getValue();\n    }\n  }, {\n    key: \"__getPlatformConfig\",\n    value: function __getPlatformConfig() {\n      return this._platformConfig;\n    }\n  }, {\n    key: \"__setPlatformConfig\",\n    value: function __setPlatformConfig(platformConfig) {\n      this._platformConfig = platformConfig;\n    }\n  }]);\n}();\nexport default AnimatedNode;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "NativeAnimatedHelper", "NativeAnimatedAPI", "API", "invariant", "_uniqueId", "AnimatedNode", "_listeners", "key", "value", "__attach", "__detach", "__isNative", "__nativeTag", "dropAnimatedNode", "undefined", "__getValue", "__getAnimatedValue", "__add<PERSON><PERSON>d", "child", "__remove<PERSON><PERSON>d", "__get<PERSON><PERSON><PERSON><PERSON>", "__makeNative", "platformConfig", "Error", "_platformConfig", "hasListeners", "_startListeningToNativeValueUpdates", "addListener", "callback", "id", "String", "removeListener", "_stopListeningForNativeValueUpdates", "removeAllListeners", "Object", "keys", "length", "_this", "__nativeAnimatedValueListener", "__shouldUpdateListenersForNewNativeTag", "startListeningToAnimatedNodeValue", "__getNativeTag", "nativeEventEmitter", "data", "tag", "__onAnimatedValueUpdateReceived", "__callListeners", "_key", "remove", "stopListeningToAnimatedNodeValue", "_this$__nativeTag", "assertNativeAnimatedModule", "nativeTag", "generateNewNodeTag", "config", "__getNativeConfig", "createAnimatedNode", "toJSON", "__getPlatformConfig", "__setPlatformConfig"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedNode.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nvar NativeAnimatedAPI = NativeAnimatedHelper.API;\nimport invariant from 'fbjs/lib/invariant';\nvar _uniqueId = 1;\n\n// Note(vjeux): this would be better as an interface but flow doesn't\n// support them yet\nclass AnimatedNode {\n  __attach() {}\n  __detach() {\n    if (this.__isNative && this.__nativeTag != null) {\n      NativeAnimatedHelper.API.dropAnimatedNode(this.__nativeTag);\n      this.__nativeTag = undefined;\n    }\n  }\n  __getValue() {}\n  __getAnimatedValue() {\n    return this.__getValue();\n  }\n  __addChild(child) {}\n  __removeChild(child) {}\n  __getChildren() {\n    return [];\n  }\n\n  /* Methods and props used by native Animated impl */\n\n  constructor() {\n    this._listeners = {};\n  }\n  __makeNative(platformConfig) {\n    if (!this.__isNative) {\n      throw new Error('This node cannot be made a \"native\" animated node');\n    }\n    this._platformConfig = platformConfig;\n    if (this.hasListeners()) {\n      this._startListeningToNativeValueUpdates();\n    }\n  }\n\n  /**\n   * Adds an asynchronous listener to the value so you can observe updates from\n   * animations.  This is useful because there is no way to\n   * synchronously read the value because it might be driven natively.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#addlistener\n   */\n  addListener(callback) {\n    var id = String(_uniqueId++);\n    this._listeners[id] = callback;\n    if (this.__isNative) {\n      this._startListeningToNativeValueUpdates();\n    }\n    return id;\n  }\n\n  /**\n   * Unregister a listener. The `id` param shall match the identifier\n   * previously returned by `addListener()`.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#removelistener\n   */\n  removeListener(id) {\n    delete this._listeners[id];\n    if (this.__isNative && !this.hasListeners()) {\n      this._stopListeningForNativeValueUpdates();\n    }\n  }\n\n  /**\n   * Remove all registered listeners.\n   *\n   * See https://reactnative.dev/docs/animatedvalue#removealllisteners\n   */\n  removeAllListeners() {\n    this._listeners = {};\n    if (this.__isNative) {\n      this._stopListeningForNativeValueUpdates();\n    }\n  }\n  hasListeners() {\n    return !!Object.keys(this._listeners).length;\n  }\n  _startListeningToNativeValueUpdates() {\n    if (this.__nativeAnimatedValueListener && !this.__shouldUpdateListenersForNewNativeTag) {\n      return;\n    }\n    if (this.__shouldUpdateListenersForNewNativeTag) {\n      this.__shouldUpdateListenersForNewNativeTag = false;\n      this._stopListeningForNativeValueUpdates();\n    }\n    NativeAnimatedAPI.startListeningToAnimatedNodeValue(this.__getNativeTag());\n    this.__nativeAnimatedValueListener = NativeAnimatedHelper.nativeEventEmitter.addListener('onAnimatedValueUpdate', data => {\n      if (data.tag !== this.__getNativeTag()) {\n        return;\n      }\n      this.__onAnimatedValueUpdateReceived(data.value);\n    });\n  }\n  __onAnimatedValueUpdateReceived(value) {\n    this.__callListeners(value);\n  }\n  __callListeners(value) {\n    for (var _key in this._listeners) {\n      this._listeners[_key]({\n        value\n      });\n    }\n  }\n  _stopListeningForNativeValueUpdates() {\n    if (!this.__nativeAnimatedValueListener) {\n      return;\n    }\n    this.__nativeAnimatedValueListener.remove();\n    this.__nativeAnimatedValueListener = null;\n    NativeAnimatedAPI.stopListeningToAnimatedNodeValue(this.__getNativeTag());\n  }\n  __getNativeTag() {\n    var _this$__nativeTag;\n    NativeAnimatedHelper.assertNativeAnimatedModule();\n    invariant(this.__isNative, 'Attempt to get native tag from node not marked as \"native\"');\n    var nativeTag = (_this$__nativeTag = this.__nativeTag) !== null && _this$__nativeTag !== void 0 ? _this$__nativeTag : NativeAnimatedHelper.generateNewNodeTag();\n    if (this.__nativeTag == null) {\n      this.__nativeTag = nativeTag;\n      var config = this.__getNativeConfig();\n      if (this._platformConfig) {\n        config.platformConfig = this._platformConfig;\n      }\n      NativeAnimatedHelper.API.createAnimatedNode(nativeTag, config);\n      this.__shouldUpdateListenersForNewNativeTag = true;\n    }\n    return nativeTag;\n  }\n  __getNativeConfig() {\n    throw new Error('This JS animated node type cannot be used as native animated node');\n  }\n  toJSON() {\n    return this.__getValue();\n  }\n  __getPlatformConfig() {\n    return this._platformConfig;\n  }\n  __setPlatformConfig(platformConfig) {\n    this._platformConfig = platformConfig;\n  }\n}\nexport default AnimatedNode;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAEb,OAAOC,oBAAoB;AAC3B,IAAIC,iBAAiB,GAAGD,oBAAoB,CAACE,GAAG;AAChD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,SAAS,GAAG,CAAC;AAAC,IAIZC,YAAY;EAoBhB,SAAAA,aAAA,EAAc;IAAAP,eAAA,OAAAO,YAAA;IACZ,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;EACtB;EAAC,OAAAP,YAAA,CAAAM,YAAA;IAAAE,GAAA;IAAAC,KAAA,EArBD,SAAAC,QAAQA,CAAA,EAAG,CAAC;EAAC;IAAAF,GAAA;IAAAC,KAAA,EACb,SAAAE,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,EAAE;QAC/CZ,oBAAoB,CAACE,GAAG,CAACW,gBAAgB,CAAC,IAAI,CAACD,WAAW,CAAC;QAC3D,IAAI,CAACA,WAAW,GAAGE,SAAS;MAC9B;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,UAAUA,CAAA,EAAG,CAAC;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACf,SAAAQ,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACD,UAAU,CAAC,CAAC;IAC1B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAS,UAAUA,CAACC,KAAK,EAAE,CAAC;EAAC;IAAAX,GAAA;IAAAC,KAAA,EACpB,SAAAW,aAAaA,CAACD,KAAK,EAAE,CAAC;EAAC;IAAAX,GAAA;IAAAC,KAAA,EACvB,SAAAY,aAAaA,CAAA,EAAG;MACd,OAAO,EAAE;IACX;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAOD,SAAAa,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACX,UAAU,EAAE;QACpB,MAAM,IAAIY,KAAK,CAAC,mDAAmD,CAAC;MACtE;MACA,IAAI,CAACC,eAAe,GAAGF,cAAc;MACrC,IAAI,IAAI,CAACG,YAAY,CAAC,CAAC,EAAE;QACvB,IAAI,CAACC,mCAAmC,CAAC,CAAC;MAC5C;IACF;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EASD,SAAAmB,WAAWA,CAACC,QAAQ,EAAE;MACpB,IAAIC,EAAE,GAAGC,MAAM,CAAC1B,SAAS,EAAE,CAAC;MAC5B,IAAI,CAACE,UAAU,CAACuB,EAAE,CAAC,GAAGD,QAAQ;MAC9B,IAAI,IAAI,CAACjB,UAAU,EAAE;QACnB,IAAI,CAACe,mCAAmC,CAAC,CAAC;MAC5C;MACA,OAAOG,EAAE;IACX;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAQD,SAAAuB,cAAcA,CAACF,EAAE,EAAE;MACjB,OAAO,IAAI,CAACvB,UAAU,CAACuB,EAAE,CAAC;MAC1B,IAAI,IAAI,CAAClB,UAAU,IAAI,CAAC,IAAI,CAACc,YAAY,CAAC,CAAC,EAAE;QAC3C,IAAI,CAACO,mCAAmC,CAAC,CAAC;MAC5C;IACF;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EAOD,SAAAyB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC3B,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,IAAI,CAACK,UAAU,EAAE;QACnB,IAAI,CAACqB,mCAAmC,CAAC,CAAC;MAC5C;IACF;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAAiB,YAAYA,CAAA,EAAG;MACb,OAAO,CAAC,CAACS,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,UAAU,CAAC,CAAC8B,MAAM;IAC9C;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EACD,SAAAkB,mCAAmCA,CAAA,EAAG;MAAA,IAAAW,KAAA;MACpC,IAAI,IAAI,CAACC,6BAA6B,IAAI,CAAC,IAAI,CAACC,sCAAsC,EAAE;QACtF;MACF;MACA,IAAI,IAAI,CAACA,sCAAsC,EAAE;QAC/C,IAAI,CAACA,sCAAsC,GAAG,KAAK;QACnD,IAAI,CAACP,mCAAmC,CAAC,CAAC;MAC5C;MACA/B,iBAAiB,CAACuC,iCAAiC,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;MAC1E,IAAI,CAACH,6BAA6B,GAAGtC,oBAAoB,CAAC0C,kBAAkB,CAACf,WAAW,CAAC,uBAAuB,EAAE,UAAAgB,IAAI,EAAI;QACxH,IAAIA,IAAI,CAACC,GAAG,KAAKP,KAAI,CAACI,cAAc,CAAC,CAAC,EAAE;UACtC;QACF;QACAJ,KAAI,CAACQ,+BAA+B,CAACF,IAAI,CAACnC,KAAK,CAAC;MAClD,CAAC,CAAC;IACJ;EAAC;IAAAD,GAAA;IAAAC,KAAA,EACD,SAAAqC,+BAA+BA,CAACrC,KAAK,EAAE;MACrC,IAAI,CAACsC,eAAe,CAACtC,KAAK,CAAC;IAC7B;EAAC;IAAAD,GAAA;IAAAC,KAAA,EACD,SAAAsC,eAAeA,CAACtC,KAAK,EAAE;MACrB,KAAK,IAAIuC,IAAI,IAAI,IAAI,CAACzC,UAAU,EAAE;QAChC,IAAI,CAACA,UAAU,CAACyC,IAAI,CAAC,CAAC;UACpBvC,KAAK,EAALA;QACF,CAAC,CAAC;MACJ;IACF;EAAC;IAAAD,GAAA;IAAAC,KAAA,EACD,SAAAwB,mCAAmCA,CAAA,EAAG;MACpC,IAAI,CAAC,IAAI,CAACM,6BAA6B,EAAE;QACvC;MACF;MACA,IAAI,CAACA,6BAA6B,CAACU,MAAM,CAAC,CAAC;MAC3C,IAAI,CAACV,6BAA6B,GAAG,IAAI;MACzCrC,iBAAiB,CAACgD,gCAAgC,CAAC,IAAI,CAACR,cAAc,CAAC,CAAC,CAAC;IAC3E;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EACD,SAAAiC,cAAcA,CAAA,EAAG;MACf,IAAIS,iBAAiB;MACrBlD,oBAAoB,CAACmD,0BAA0B,CAAC,CAAC;MACjDhD,SAAS,CAAC,IAAI,CAACQ,UAAU,EAAE,4DAA4D,CAAC;MACxF,IAAIyC,SAAS,GAAG,CAACF,iBAAiB,GAAG,IAAI,CAACtC,WAAW,MAAM,IAAI,IAAIsC,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGlD,oBAAoB,CAACqD,kBAAkB,CAAC,CAAC;MAC/J,IAAI,IAAI,CAACzC,WAAW,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACA,WAAW,GAAGwC,SAAS;QAC5B,IAAIE,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC/B,eAAe,EAAE;UACxB8B,MAAM,CAAChC,cAAc,GAAG,IAAI,CAACE,eAAe;QAC9C;QACAxB,oBAAoB,CAACE,GAAG,CAACsD,kBAAkB,CAACJ,SAAS,EAAEE,MAAM,CAAC;QAC9D,IAAI,CAACf,sCAAsC,GAAG,IAAI;MACpD;MACA,OAAOa,SAAS;IAClB;EAAC;IAAA7C,GAAA;IAAAC,KAAA,EACD,SAAA+C,iBAAiBA,CAAA,EAAG;MAClB,MAAM,IAAIhC,KAAK,CAAC,mEAAmE,CAAC;IACtF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EACD,SAAAiD,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC1C,UAAU,CAAC,CAAC;IAC1B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAkD,mBAAmBA,CAAA,EAAG;MACpB,OAAO,IAAI,CAAClC,eAAe;IAC7B;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAmD,mBAAmBA,CAACrC,cAAc,EAAE;MAClC,IAAI,CAACE,eAAe,GAAGF,cAAc;IACvC;EAAC;AAAA;AAEH,eAAejB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}