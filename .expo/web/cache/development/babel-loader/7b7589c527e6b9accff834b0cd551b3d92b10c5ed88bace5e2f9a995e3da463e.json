{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { EventEmitter } from 'expo-modules-core';\nimport React, { useEffect, useState, useRef, useMemo } from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport View from \"react-native-web/dist/exports/View\";\nimport DevLoadingViewNativeModule from \"./DevLoadingViewNativeModule\";\nimport { getInitialSafeArea } from \"./getInitialSafeArea\";\nexport default function DevLoadingView() {\n  var _useState = useState('Refreshing...'),\n    _useState2 = _slicedToArray(_useState, 2),\n    message = _useState2[0],\n    setMessage = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    isDevLoading = _useState4[0],\n    setIsDevLoading = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    isAnimating = _useState6[0],\n    setIsAnimating = _useState6[1];\n  var translateY = useRef(new Animated.Value(0)).current;\n  var emitter = useMemo(function () {\n    try {\n      return new EventEmitter(DevLoadingViewNativeModule);\n    } catch (error) {\n      throw new Error('Failed to instantiate native emitter in `DevLoadingView` because the native module `DevLoadingView` is undefined: ' + error.message);\n    }\n  }, []);\n  useEffect(function () {\n    if (!emitter) return;\n    function handleShowMessage(event) {\n      setMessage(event.message);\n      translateY.setValue(0);\n      setIsDevLoading(true);\n    }\n    function handleHide() {\n      setIsAnimating(true);\n      setIsDevLoading(false);\n      Animated.timing(translateY, {\n        toValue: 150,\n        delay: 1000,\n        duration: 350,\n        useNativeDriver: Platform.OS !== 'web'\n      }).start(function (_ref) {\n        var finished = _ref.finished;\n        if (finished) {\n          setIsAnimating(false);\n          translateY.setValue(0);\n        }\n      });\n    }\n    var showMessageSubscription = emitter.addListener('devLoadingView:showMessage', handleShowMessage);\n    var hideSubscription = emitter.addListener('devLoadingView:hide', handleHide);\n    return function cleanup() {\n      showMessageSubscription.remove();\n      hideSubscription.remove();\n    };\n  }, [translateY, emitter]);\n  if (!isDevLoading && !isAnimating) {\n    return null;\n  }\n  return React.createElement(Animated.View, {\n    style: [styles.animatedContainer, {\n      transform: [{\n        translateY: translateY\n      }]\n    }],\n    pointerEvents: \"none\"\n  }, React.createElement(View, {\n    style: styles.banner\n  }, React.createElement(View, {\n    style: styles.contentContainer\n  }, React.createElement(View, {\n    style: {\n      flexDirection: 'row'\n    }\n  }, React.createElement(Text, {\n    style: styles.text\n  }, message)), React.createElement(View, {\n    style: {\n      flex: 1\n    }\n  }, React.createElement(Text, {\n    style: styles.subtitle\n  }, isDevLoading ? 'Using Fast Refresh' : \"Don't see your changes? Reload the app\")))));\n}\nvar styles = StyleSheet.create({\n  animatedContainer: {\n    position: Platform.select({\n      web: 'fixed',\n      default: 'absolute'\n    }),\n    bottom: 0,\n    left: 0,\n    right: 0,\n    zIndex: 42\n  },\n  banner: {\n    flex: 1,\n    overflow: 'visible',\n    backgroundColor: 'rgba(0,0,0,0.75)',\n    paddingBottom: getInitialSafeArea().bottom\n  },\n  contentContainer: {\n    flex: 1,\n    paddingTop: 10,\n    paddingBottom: 5,\n    alignItems: 'center',\n    justifyContent: 'center',\n    textAlign: 'center'\n  },\n  text: {\n    color: '#fff',\n    fontSize: 15\n  },\n  subtitle: {\n    color: 'rgba(255,255,255,0.8)'\n  }\n});", "map": {"version": 3, "names": ["EventEmitter", "React", "useEffect", "useState", "useRef", "useMemo", "Animated", "StyleSheet", "Text", "Platform", "View", "DevLoadingViewNativeModule", "getInitialSafeArea", "DevLoadingView", "_useState", "_useState2", "_slicedToArray", "message", "setMessage", "_useState3", "_useState4", "isDevLoading", "setIsDevLoading", "_useState5", "_useState6", "isAnimating", "setIsAnimating", "translateY", "Value", "current", "emitter", "error", "Error", "handleShowMessage", "event", "setValue", "handleHide", "timing", "toValue", "delay", "duration", "useNativeDriver", "OS", "start", "_ref", "finished", "showMessageSubscription", "addListener", "hideSubscription", "cleanup", "remove", "createElement", "style", "styles", "animatedC<PERSON><PERSON>", "transform", "pointerEvents", "banner", "contentContainer", "flexDirection", "text", "flex", "subtitle", "create", "position", "select", "web", "default", "bottom", "left", "right", "zIndex", "overflow", "backgroundColor", "paddingBottom", "paddingTop", "alignItems", "justifyContent", "textAlign", "color", "fontSize"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/expo/src/environment/DevLoadingView.tsx"], "sourcesContent": ["import { EventEmitter } from 'expo-modules-core';\nimport React, { useEffect, useState, useRef, useMemo } from 'react';\nimport { Animated, StyleSheet, Text, Platform, View } from 'react-native';\n\nimport DevLoadingViewNativeModule from './DevLoadingViewNativeModule';\nimport { getInitialSafeArea } from './getInitialSafeArea';\n\nexport default function DevLoadingView() {\n  const [message, setMessage] = useState('Refreshing...');\n  const [isDevLoading, setIsDevLoading] = useState(false);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const translateY = useRef(new Animated.Value(0)).current;\n  const emitter = useMemo<EventEmitter>(() => {\n    try {\n      return new EventEmitter(DevLoadingViewNativeModule);\n    } catch (error) {\n      throw new Error(\n        'Failed to instantiate native emitter in `DevLoadingView` because the native module `DevLoadingView` is undefined: ' +\n          error.message\n      );\n    }\n  }, []);\n\n  useEffect(() => {\n    if (!emitter) return;\n\n    function handleShowMessage(event: { message: string }) {\n      setMessage(event.message);\n      // TODO: if we show the refreshing banner and don't get a hide message\n      // for 3 seconds, warn the user that it's taking a while and suggest\n      // they reload\n\n      translateY.setValue(0);\n      setIsDevLoading(true);\n    }\n\n    function handleHide() {\n      // TODO: if we showed the 'refreshing' banner less than 250ms ago, delay\n      // switching to the 'finished' banner\n\n      setIsAnimating(true);\n      setIsDevLoading(false);\n      Animated.timing(translateY, {\n        toValue: 150,\n        delay: 1000,\n        duration: 350,\n        useNativeDriver: Platform.OS !== 'web',\n      }).start(({ finished }) => {\n        if (finished) {\n          setIsAnimating(false);\n          translateY.setValue(0);\n        }\n      });\n    }\n\n    const showMessageSubscription = emitter.addListener(\n      'devLoadingView:showMessage',\n      handleShowMessage\n    );\n    const hideSubscription = emitter.addListener('devLoadingView:hide', handleHide);\n\n    return function cleanup() {\n      showMessageSubscription.remove();\n      hideSubscription.remove();\n    };\n  }, [translateY, emitter]);\n\n  if (!isDevLoading && !isAnimating) {\n    return null;\n  }\n\n  return (\n    <Animated.View\n      style={[styles.animatedContainer, { transform: [{ translateY }] }]}\n      pointerEvents=\"none\">\n      <View style={styles.banner}>\n        <View style={styles.contentContainer}>\n          <View style={{ flexDirection: 'row' }}>\n            <Text style={styles.text}>{message}</Text>\n          </View>\n\n          <View style={{ flex: 1 }}>\n            <Text style={styles.subtitle}>\n              {isDevLoading ? 'Using Fast Refresh' : \"Don't see your changes? Reload the app\"}\n            </Text>\n          </View>\n        </View>\n      </View>\n    </Animated.View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  animatedContainer: {\n    // @ts-expect-error: fixed is not a valid value for position in Yoga but it is on web.\n    position: Platform.select({\n      web: 'fixed',\n      default: 'absolute',\n    }),\n    bottom: 0,\n    left: 0,\n    right: 0,\n    zIndex: 42, // arbitrary\n  },\n\n  banner: {\n    flex: 1,\n    overflow: 'visible',\n    backgroundColor: 'rgba(0,0,0,0.75)',\n    paddingBottom: getInitialSafeArea().bottom,\n  },\n  contentContainer: {\n    flex: 1,\n    paddingTop: 10,\n    paddingBottom: 5,\n    alignItems: 'center',\n    justifyContent: 'center',\n    textAlign: 'center',\n  },\n  text: {\n    color: '#fff',\n    fontSize: 15,\n  },\n  subtitle: {\n    color: 'rgba(255,255,255,0.8)',\n  },\n});\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAAC,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,IAAA;AAGpE,OAAOC,0BAA0B;AACjC,SAASC,kBAAkB;AAE3B,eAAc,SAAUC,cAAcA,CAAA;EACpC,IAAAC,SAAA,GAA8BX,QAAQ,CAAC,eAAe,CAAC;IAAAY,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAhDG,OAAO,GAAAF,UAAA;IAAEG,UAAU,GAAAH,UAAA;EAC1B,IAAAI,UAAA,GAAwChB,QAAQ,CAAC,KAAK,CAAC;IAAAiB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAhDE,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EACpC,IAAAG,UAAA,GAAsCpB,QAAQ,CAAC,KAAK,CAAC;IAAAqB,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA9CE,WAAW,GAAAD,UAAA;IAAEE,cAAc,GAAAF,UAAA;EAClC,IAAMG,UAAU,GAAGvB,MAAM,CAAC,IAAIE,QAAQ,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EACxD,IAAMC,OAAO,GAAGzB,OAAO,CAAe,YAAK;IACzC,IAAI;MACF,OAAO,IAAIL,YAAY,CAACW,0BAA0B,CAAC;KACpD,CAAC,OAAOoB,KAAK,EAAE;MACd,MAAM,IAAIC,KAAK,CACb,oHAAoH,GAClHD,KAAK,CAACd,OAAO,CAChB;;EAEL,CAAC,EAAE,EAAE,CAAC;EAENf,SAAS,CAAC,YAAK;IACb,IAAI,CAAC4B,OAAO,EAAE;IAEd,SAASG,iBAAiBA,CAACC,KAA0B;MACnDhB,UAAU,CAACgB,KAAK,CAACjB,OAAO,CAAC;MAKzBU,UAAU,CAACQ,QAAQ,CAAC,CAAC,CAAC;MACtBb,eAAe,CAAC,IAAI,CAAC;IACvB;IAEA,SAASc,UAAUA,CAAA;MAIjBV,cAAc,CAAC,IAAI,CAAC;MACpBJ,eAAe,CAAC,KAAK,CAAC;MACtBhB,QAAQ,CAAC+B,MAAM,CAACV,UAAU,EAAE;QAC1BW,OAAO,EAAE,GAAG;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAEhC,QAAQ,CAACiC,EAAE,KAAK;OAClC,CAAC,CAACC,KAAK,CAAC,UAAAC,IAAA,EAAiB;QAAA,IAAdC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;QAClB,IAAIA,QAAQ,EAAE;UACZnB,cAAc,CAAC,KAAK,CAAC;UACrBC,UAAU,CAACQ,QAAQ,CAAC,CAAC,CAAC;;MAE1B,CAAC,CAAC;IACJ;IAEA,IAAMW,uBAAuB,GAAGhB,OAAO,CAACiB,WAAW,CACjD,4BAA4B,EAC5Bd,iBAAiB,CAClB;IACD,IAAMe,gBAAgB,GAAGlB,OAAO,CAACiB,WAAW,CAAC,qBAAqB,EAAEX,UAAU,CAAC;IAE/E,OAAO,SAASa,OAAOA,CAAA;MACrBH,uBAAuB,CAACI,MAAM,EAAE;MAChCF,gBAAgB,CAACE,MAAM,EAAE;IAC3B,CAAC;EACH,CAAC,EAAE,CAACvB,UAAU,EAAEG,OAAO,CAAC,CAAC;EAEzB,IAAI,CAACT,YAAY,IAAI,CAACI,WAAW,EAAE;IACjC,OAAO,IAAI;;EAGb,OACExB,KAAA,CAAAkD,aAAA,CAAC7C,QAAQ,CAACI,IAAI;IACZ0C,KAAK,EAAE,CAACC,MAAM,CAACC,iBAAiB,EAAE;MAAEC,SAAS,EAAE,CAAC;QAAE5B,UAAU,EAAVA;MAAU,CAAE;IAAC,CAAE,CAAC;IAClE6B,aAAa,EAAC;EAAM,GACpBvD,KAAA,CAAAkD,aAAA,CAACzC,IAAI;IAAC0C,KAAK,EAAEC,MAAM,CAACI;EAAM,GACxBxD,KAAA,CAAAkD,aAAA,CAACzC,IAAI;IAAC0C,KAAK,EAAEC,MAAM,CAACK;EAAgB,GAClCzD,KAAA,CAAAkD,aAAA,CAACzC,IAAI;IAAC0C,KAAK,EAAE;MAAEO,aAAa,EAAE;IAAK;EAAE,GACnC1D,KAAA,CAAAkD,aAAA,CAAC3C,IAAI;IAAC4C,KAAK,EAAEC,MAAM,CAACO;EAAI,GAAG3C,OAAO,CAAQ,CACrC,EAEPhB,KAAA,CAAAkD,aAAA,CAACzC,IAAI;IAAC0C,KAAK,EAAE;MAAES,IAAI,EAAE;IAAC;EAAE,GACtB5D,KAAA,CAAAkD,aAAA,CAAC3C,IAAI;IAAC4C,KAAK,EAAEC,MAAM,CAACS;EAAQ,GACzBzC,YAAY,GAAG,oBAAoB,GAAG,wCAAwC,CAC1E,CACF,CACF,CACF,CACO;AAEpB;AAEA,IAAMgC,MAAM,GAAG9C,UAAU,CAACwD,MAAM,CAAC;EAC/BT,iBAAiB,EAAE;IAEjBU,QAAQ,EAAEvD,QAAQ,CAACwD,MAAM,CAAC;MACxBC,GAAG,EAAE,OAAO;MACZC,OAAO,EAAE;KACV,CAAC;IACFC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;GACT;EAEDd,MAAM,EAAE;IACNI,IAAI,EAAE,CAAC;IACPW,QAAQ,EAAE,SAAS;IACnBC,eAAe,EAAE,kBAAkB;IACnCC,aAAa,EAAE9D,kBAAkB,EAAE,CAACwD;GACrC;EACDV,gBAAgB,EAAE;IAChBG,IAAI,EAAE,CAAC;IACPc,UAAU,EAAE,EAAE;IACdD,aAAa,EAAE,CAAC;IAChBE,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,SAAS,EAAE;GACZ;EACDlB,IAAI,EAAE;IACJmB,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE;GACX;EACDlB,QAAQ,EAAE;IACRiB,KAAK,EAAE;;CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}