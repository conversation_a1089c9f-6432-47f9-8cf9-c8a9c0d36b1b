{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport AppContainer from \"./AppContainer\";\nimport invariant from 'fbjs/lib/invariant';\nimport renderLegacy, { hydrateLegacy, render, hydrate } from \"../render\";\nimport StyleSheet from \"../StyleSheet\";\nimport React from 'react';\nexport default function renderApplication(RootComponent, WrapperComponent, callback, options) {\n  var shouldHydrate = options.hydrate,\n    initialProps = options.initialProps,\n    mode = options.mode,\n    rootTag = options.rootTag;\n  var renderFn = shouldHydrate ? mode === 'concurrent' ? hydrate : hydrateLegacy : mode === 'concurrent' ? render : renderLegacy;\n  invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n  return renderFn(React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    ref: callback,\n    rootTag: rootTag\n  }, React.createElement(RootComponent, initialProps)), rootTag);\n}\nexport function getApplication(RootComponent, initialProps, WrapperComponent) {\n  var element = React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    rootTag: {}\n  }, React.createElement(RootComponent, initialProps));\n  var getStyleElement = function getStyleElement(props) {\n    var sheet = StyleSheet.getSheet();\n    return React.createElement(\"style\", _extends({}, props, {\n      dangerouslySetInnerHTML: {\n        __html: sheet.textContent\n      },\n      id: sheet.id\n    }));\n  };\n  return {\n    element: element,\n    getStyleElement: getStyleElement\n  };\n}", "map": {"version": 3, "names": ["_extends", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invariant", "renderLegacy", "hydrateLegacy", "render", "hydrate", "StyleSheet", "React", "renderApplication", "RootComponent", "WrapperComponent", "callback", "options", "shouldHydrate", "initialProps", "mode", "rootTag", "renderFn", "createElement", "ref", "getApplication", "element", "getStyleElement", "props", "sheet", "getSheet", "dangerouslySetInnerHTML", "__html", "textContent", "id"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/AppRegistry/renderApplication.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport AppContainer from './AppContainer';\nimport invariant from 'fbjs/lib/invariant';\nimport renderLegacy, { hydrateLegacy, render, hydrate } from '../render';\nimport StyleSheet from '../StyleSheet';\nimport React from 'react';\nexport default function renderApplication(RootComponent, WrapperComponent, callback, options) {\n  var shouldHydrate = options.hydrate,\n    initialProps = options.initialProps,\n    mode = options.mode,\n    rootTag = options.rootTag;\n  var renderFn = shouldHydrate ? mode === 'concurrent' ? hydrate : hydrateLegacy : mode === 'concurrent' ? render : renderLegacy;\n  invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n  return renderFn(/*#__PURE__*/React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    ref: callback,\n    rootTag: rootTag\n  }, /*#__PURE__*/React.createElement(RootComponent, initialProps)), rootTag);\n}\nexport function getApplication(RootComponent, initialProps, WrapperComponent) {\n  var element = /*#__PURE__*/React.createElement(AppContainer, {\n    WrapperComponent: WrapperComponent,\n    rootTag: {}\n  }, /*#__PURE__*/React.createElement(RootComponent, initialProps));\n  // Don't escape CSS text\n  var getStyleElement = props => {\n    var sheet = StyleSheet.getSheet();\n    return /*#__PURE__*/React.createElement(\"style\", _extends({}, props, {\n      dangerouslySetInnerHTML: {\n        __html: sheet.textContent\n      },\n      id: sheet.id\n    }));\n  };\n  return {\n    element,\n    getStyleElement\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AAWrD,OAAOC,YAAY;AACnB,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,YAAY,IAAIC,aAAa,EAAEC,MAAM,EAAEC,OAAO;AACrD,OAAOC,UAAU;AACjB,OAAOC,KAAK,MAAM,OAAO;AACzB,eAAe,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC5F,IAAIC,aAAa,GAAGD,OAAO,CAACP,OAAO;IACjCS,YAAY,GAAGF,OAAO,CAACE,YAAY;IACnCC,IAAI,GAAGH,OAAO,CAACG,IAAI;IACnBC,OAAO,GAAGJ,OAAO,CAACI,OAAO;EAC3B,IAAIC,QAAQ,GAAGJ,aAAa,GAAGE,IAAI,KAAK,YAAY,GAAGV,OAAO,GAAGF,aAAa,GAAGY,IAAI,KAAK,YAAY,GAAGX,MAAM,GAAGF,YAAY;EAC9HD,SAAS,CAACe,OAAO,EAAE,8CAA8C,EAAEA,OAAO,CAAC;EAC3E,OAAOC,QAAQ,CAAcV,KAAK,CAACW,aAAa,CAAClB,YAAY,EAAE;IAC7DU,gBAAgB,EAAEA,gBAAgB;IAClCS,GAAG,EAAER,QAAQ;IACbK,OAAO,EAAEA;EACX,CAAC,EAAeT,KAAK,CAACW,aAAa,CAACT,aAAa,EAAEK,YAAY,CAAC,CAAC,EAAEE,OAAO,CAAC;AAC7E;AACA,OAAO,SAASI,cAAcA,CAACX,aAAa,EAAEK,YAAY,EAAEJ,gBAAgB,EAAE;EAC5E,IAAIW,OAAO,GAAgBd,KAAK,CAACW,aAAa,CAAClB,YAAY,EAAE;IAC3DU,gBAAgB,EAAEA,gBAAgB;IAClCM,OAAO,EAAE,CAAC;EACZ,CAAC,EAAeT,KAAK,CAACW,aAAa,CAACT,aAAa,EAAEK,YAAY,CAAC,CAAC;EAEjE,IAAIQ,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAK,EAAI;IAC7B,IAAIC,KAAK,GAAGlB,UAAU,CAACmB,QAAQ,CAAC,CAAC;IACjC,OAAoBlB,KAAK,CAACW,aAAa,CAAC,OAAO,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;MACnEG,uBAAuB,EAAE;QACvBC,MAAM,EAAEH,KAAK,CAACI;MAChB,CAAC;MACDC,EAAE,EAAEL,KAAK,CAACK;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAO;IACLR,OAAO,EAAPA,OAAO;IACPC,eAAe,EAAfA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}