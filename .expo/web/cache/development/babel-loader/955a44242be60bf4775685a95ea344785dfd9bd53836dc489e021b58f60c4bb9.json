{"ast": null, "code": "import { findDOMNode } from 'react-dom';\nvar findNodeHandle = function findNodeHandle(component) {\n  var node;\n  try {\n    node = findDOMNode(component);\n  } catch (e) {}\n  return node;\n};\nexport default findNodeHandle;", "map": {"version": 3, "names": ["findDOMNode", "findNodeHandle", "component", "node", "e"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/findNodeHandle/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { findDOMNode } from 'react-dom';\n\n/**\n * @deprecated imperatively finding the DOM element of a react component has been deprecated in React 18.\n * You should use ref properties on the component instead.\n */\nvar findNodeHandle = component => {\n  var node;\n  try {\n    node = findDOMNode(component);\n  } catch (e) {}\n  return node;\n};\nexport default findNodeHandle;"], "mappings": "AAUA,SAASA,WAAW,QAAQ,WAAW;AAMvC,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,SAAS,EAAI;EAChC,IAAIC,IAAI;EACR,IAAI;IACFA,IAAI,GAAGH,WAAW,CAACE,SAAS,CAAC;EAC/B,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;EACb,OAAOD,IAAI;AACb,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}