{"ast": null, "code": "'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent, attachNativeEvent } from \"./AnimatedEvent\";\nimport AnimatedImplementation from \"./AnimatedImplementation\";\nimport AnimatedInterpolation from \"./nodes/AnimatedInterpolation\";\nimport AnimatedNode from \"./nodes/AnimatedNode\";\nimport AnimatedValue from \"./nodes/AnimatedValue\";\nimport AnimatedValueXY from \"./nodes/AnimatedValueXY\";\nimport createAnimatedComponent from \"./createAnimatedComponent\";\nimport AnimatedColor from \"./nodes/AnimatedColor\";\nvar inAnimationCallback = false;\nfunction mockAnimationStart(start) {\n  return function (callback) {\n    var guardedCallback = callback == null ? callback : function () {\n      if (inAnimationCallback) {\n        console.warn('Ignoring recursive animation callback when running mock animations');\n        return;\n      }\n      inAnimationCallback = true;\n      try {\n        callback.apply(void 0, arguments);\n      } finally {\n        inAnimationCallback = false;\n      }\n    };\n    start(guardedCallback);\n  };\n}\nvar emptyAnimation = {\n  start: function start() {},\n  stop: function stop() {},\n  reset: function reset() {},\n  _startNativeLoop: function _startNativeLoop() {},\n  _isUsingNativeDriver: function _isUsingNativeDriver() {\n    return false;\n  }\n};\nvar mockCompositeAnimation = function mockCompositeAnimation(animations) {\n  return _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n    start: mockAnimationStart(function (callback) {\n      animations.forEach(function (animation) {\n        return animation.start();\n      });\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar spring = function spring(value, config) {\n  var anyValue = value;\n  return _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n    start: mockAnimationStart(function (callback) {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar timing = function timing(value, config) {\n  var anyValue = value;\n  return _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n    start: mockAnimationStart(function (callback) {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar decay = function decay(value, config) {\n  return emptyAnimation;\n};\nvar sequence = function sequence(animations) {\n  return mockCompositeAnimation(animations);\n};\nvar parallel = function parallel(animations, config) {\n  return mockCompositeAnimation(animations);\n};\nvar delay = function delay(time) {\n  return emptyAnimation;\n};\nvar stagger = function stagger(time, animations) {\n  return mockCompositeAnimation(animations);\n};\nvar loop = function loop(animation, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$iterations = _ref.iterations,\n    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;\n  return emptyAnimation;\n};\nexport default {\n  Value: AnimatedValue,\n  ValueXY: AnimatedValueXY,\n  Color: AnimatedColor,\n  Interpolation: AnimatedInterpolation,\n  Node: AnimatedNode,\n  decay: decay,\n  timing: timing,\n  spring: spring,\n  add: AnimatedImplementation.add,\n  subtract: AnimatedImplementation.subtract,\n  divide: AnimatedImplementation.divide,\n  multiply: AnimatedImplementation.multiply,\n  modulo: AnimatedImplementation.modulo,\n  diffClamp: AnimatedImplementation.diffClamp,\n  delay: delay,\n  sequence: sequence,\n  parallel: parallel,\n  stagger: stagger,\n  loop: loop,\n  event: AnimatedImplementation.event,\n  createAnimatedComponent: createAnimatedComponent,\n  attachNativeEvent: attachNativeEvent,\n  forkEvent: AnimatedImplementation.forkEvent,\n  unforkEvent: AnimatedImplementation.unforkEvent,\n  Event: AnimatedEvent\n};", "map": {"version": 3, "names": ["_objectSpread", "AnimatedEvent", "attachNativeEvent", "AnimatedImplementation", "AnimatedInterpolation", "AnimatedNode", "AnimatedValue", "AnimatedValueXY", "createAnimatedComponent", "AnimatedColor", "inAnimationCallback", "mockAnimationStart", "start", "callback", "guarded<PERSON><PERSON>back", "console", "warn", "apply", "arguments", "emptyAnimation", "stop", "reset", "_startNativeLoop", "_isUsingNativeDriver", "mockCompositeAnimation", "animations", "for<PERSON>ach", "animation", "finished", "spring", "value", "config", "anyValue", "setValue", "toValue", "timing", "decay", "sequence", "parallel", "delay", "time", "stagger", "loop", "_temp", "_ref", "_ref$iterations", "iterations", "Value", "ValueXY", "Color", "Interpolation", "Node", "add", "subtract", "divide", "multiply", "modulo", "diffClamp", "event", "forkEvent", "unforkEvent", "Event"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/AnimatedMock.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport { AnimatedEvent, attachNativeEvent } from './AnimatedEvent';\nimport AnimatedImplementation from './AnimatedImplementation';\nimport AnimatedInterpolation from './nodes/AnimatedInterpolation';\nimport AnimatedNode from './nodes/AnimatedNode';\nimport AnimatedValue from './nodes/AnimatedValue';\nimport AnimatedValueXY from './nodes/AnimatedValueXY';\nimport createAnimatedComponent from './createAnimatedComponent';\nimport AnimatedColor from './nodes/AnimatedColor';\n\n/**\n * Animations are a source of flakiness in snapshot testing. This mock replaces\n * animation functions from AnimatedImplementation with empty animations for\n * predictability in tests. When possible the animation will run immediately\n * to the final state.\n */\n\n// Prevent any callback invocation from recursively triggering another\n// callback, which may trigger another animation\nvar inAnimationCallback = false;\nfunction mockAnimationStart(start) {\n  return callback => {\n    var guardedCallback = callback == null ? callback : function () {\n      if (inAnimationCallback) {\n        console.warn('Ignoring recursive animation callback when running mock animations');\n        return;\n      }\n      inAnimationCallback = true;\n      try {\n        callback(...arguments);\n      } finally {\n        inAnimationCallback = false;\n      }\n    };\n    start(guardedCallback);\n  };\n}\nvar emptyAnimation = {\n  start: () => {},\n  stop: () => {},\n  reset: () => {},\n  _startNativeLoop: () => {},\n  _isUsingNativeDriver: () => {\n    return false;\n  }\n};\nvar mockCompositeAnimation = animations => _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n  start: mockAnimationStart(callback => {\n    animations.forEach(animation => animation.start());\n    callback == null ? void 0 : callback({\n      finished: true\n    });\n  })\n});\nvar spring = function spring(value, config) {\n  var anyValue = value;\n  return _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n    start: mockAnimationStart(callback => {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar timing = function timing(value, config) {\n  var anyValue = value;\n  return _objectSpread(_objectSpread({}, emptyAnimation), {}, {\n    start: mockAnimationStart(callback => {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar decay = function decay(value, config) {\n  return emptyAnimation;\n};\nvar sequence = function sequence(animations) {\n  return mockCompositeAnimation(animations);\n};\nvar parallel = function parallel(animations, config) {\n  return mockCompositeAnimation(animations);\n};\nvar delay = function delay(time) {\n  return emptyAnimation;\n};\nvar stagger = function stagger(time, animations) {\n  return mockCompositeAnimation(animations);\n};\nvar loop = function loop(animation, // $FlowFixMe[prop-missing]\n_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$iterations = _ref.iterations,\n    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;\n  return emptyAnimation;\n};\nexport default {\n  Value: AnimatedValue,\n  ValueXY: AnimatedValueXY,\n  Color: AnimatedColor,\n  Interpolation: AnimatedInterpolation,\n  Node: AnimatedNode,\n  decay,\n  timing,\n  spring,\n  add: AnimatedImplementation.add,\n  subtract: AnimatedImplementation.subtract,\n  divide: AnimatedImplementation.divide,\n  multiply: AnimatedImplementation.multiply,\n  modulo: AnimatedImplementation.modulo,\n  diffClamp: AnimatedImplementation.diffClamp,\n  delay,\n  sequence,\n  parallel,\n  stagger,\n  loop,\n  event: AnimatedImplementation.event,\n  createAnimatedComponent,\n  attachNativeEvent,\n  forkEvent: AnimatedImplementation.forkEvent,\n  unforkEvent: AnimatedImplementation.unforkEvent,\n  Event: AnimatedEvent\n};"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,SAASC,aAAa,EAAEC,iBAAiB;AACzC,OAAOC,sBAAsB;AAC7B,OAAOC,qBAAqB;AAC5B,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,eAAe;AACtB,OAAOC,uBAAuB;AAC9B,OAAOC,aAAa;AAWpB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,UAAAC,QAAQ,EAAI;IACjB,IAAIC,eAAe,GAAGD,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,YAAY;MAC9D,IAAIH,mBAAmB,EAAE;QACvBK,OAAO,CAACC,IAAI,CAAC,oEAAoE,CAAC;QAClF;MACF;MACAN,mBAAmB,GAAG,IAAI;MAC1B,IAAI;QACFG,QAAQ,CAAAI,KAAA,SAAIC,SAAS,CAAC;MACxB,CAAC,SAAS;QACRR,mBAAmB,GAAG,KAAK;MAC7B;IACF,CAAC;IACDE,KAAK,CAACE,eAAe,CAAC;EACxB,CAAC;AACH;AACA,IAAIK,cAAc,GAAG;EACnBP,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfQ,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ,CAAC,CAAC;EACdC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAQ,CAAC,CAAC;EAC1BC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAQ;IAC1B,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAGC,UAAU;EAAA,OAAIzB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IAC9FP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCY,UAAU,CAACC,OAAO,CAAC,UAAAC,SAAS;QAAA,OAAIA,SAAS,CAACf,KAAK,CAAC,CAAC;MAAA,EAAC;MAClDC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AAAA;AACF,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC1C,IAAIC,QAAQ,GAAGF,KAAK;EACpB,OAAO9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1DP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAIO,MAAM,GAAG,SAASA,MAAMA,CAACL,KAAK,EAAEC,MAAM,EAAE;EAC1C,IAAIC,QAAQ,GAAGF,KAAK;EACpB,OAAO9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1DP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAIQ,KAAK,GAAG,SAASA,KAAKA,CAACN,KAAK,EAAEC,MAAM,EAAE;EACxC,OAAOZ,cAAc;AACvB,CAAC;AACD,IAAIkB,QAAQ,GAAG,SAASA,QAAQA,CAACZ,UAAU,EAAE;EAC3C,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIa,QAAQ,GAAG,SAASA,QAAQA,CAACb,UAAU,EAAEM,MAAM,EAAE;EACnD,OAAOP,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIc,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,OAAOrB,cAAc;AACvB,CAAC;AACD,IAAIsB,OAAO,GAAG,SAASA,OAAOA,CAACD,IAAI,EAAEf,UAAU,EAAE;EAC/C,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIiB,IAAI,GAAG,SAASA,IAAIA,CAACf,SAAS,EAClCgB,KAAK,EAAE;EACL,IAAIC,IAAI,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;IACtCE,eAAe,GAAGD,IAAI,CAACE,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;EAChE,OAAO1B,cAAc;AACvB,CAAC;AACD,eAAe;EACb4B,KAAK,EAAEzC,aAAa;EACpB0C,OAAO,EAAEzC,eAAe;EACxB0C,KAAK,EAAExC,aAAa;EACpByC,aAAa,EAAE9C,qBAAqB;EACpC+C,IAAI,EAAE9C,YAAY;EAClB+B,KAAK,EAALA,KAAK;EACLD,MAAM,EAANA,MAAM;EACNN,MAAM,EAANA,MAAM;EACNuB,GAAG,EAAEjD,sBAAsB,CAACiD,GAAG;EAC/BC,QAAQ,EAAElD,sBAAsB,CAACkD,QAAQ;EACzCC,MAAM,EAAEnD,sBAAsB,CAACmD,MAAM;EACrCC,QAAQ,EAAEpD,sBAAsB,CAACoD,QAAQ;EACzCC,MAAM,EAAErD,sBAAsB,CAACqD,MAAM;EACrCC,SAAS,EAAEtD,sBAAsB,CAACsD,SAAS;EAC3ClB,KAAK,EAALA,KAAK;EACLF,QAAQ,EAARA,QAAQ;EACRC,QAAQ,EAARA,QAAQ;EACRG,OAAO,EAAPA,OAAO;EACPC,IAAI,EAAJA,IAAI;EACJgB,KAAK,EAAEvD,sBAAsB,CAACuD,KAAK;EACnClD,uBAAuB,EAAvBA,uBAAuB;EACvBN,iBAAiB,EAAjBA,iBAAiB;EACjByD,SAAS,EAAExD,sBAAsB,CAACwD,SAAS;EAC3CC,WAAW,EAAEzD,sBAAsB,CAACyD,WAAW;EAC/CC,KAAK,EAAE5D;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}