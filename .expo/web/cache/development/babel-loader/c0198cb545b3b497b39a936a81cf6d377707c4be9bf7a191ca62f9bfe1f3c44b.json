{"ast": null, "code": "import View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport Icon from \"./Icon\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ServiceProviderCard = function ServiceProviderCard(_ref) {\n  var provider = _ref.provider;\n  return _jsxs(View, {\n    style: styles.card,\n    children: [_jsx(Image, {\n      source: {\n        uri: provider.image\n      },\n      style: styles.image\n    }), _jsxs(View, {\n      style: styles.content,\n      children: [_jsxs(View, {\n        style: styles.header,\n        children: [_jsx(Text, {\n          style: styles.name,\n          children: provider.name\n        }), _jsxs(View, {\n          style: styles.rating,\n          children: [_jsx(Icon, {\n            name: \"star\",\n            size: 16,\n            color: \"#FFD700\"\n          }), _jsx(Text, {\n            style: styles.ratingText,\n            children: provider.rating\n          })]\n        })]\n      }), _jsxs(View, {\n        style: styles.location,\n        children: [_jsx(Icon, {\n          name: \"map-pin\",\n          size: 16,\n          color: \"#666\"\n        }), _jsx(Text, {\n          style: styles.locationText,\n          children: provider.location\n        })]\n      }), _jsx(View, {\n        style: styles.services,\n        children: provider.services.map(function (service, index) {\n          return _jsx(View, {\n            style: styles.serviceTag,\n            children: _jsx(Text, {\n              style: styles.serviceText,\n              children: service\n            })\n          }, index);\n        })\n      }), _jsx(Text, {\n        style: styles.description,\n        children: provider.description\n      }), _jsxs(View, {\n        style: styles.actions,\n        children: [_jsx(TouchableOpacity, {\n          style: styles.requestButton,\n          children: _jsx(Text, {\n            style: styles.requestButtonText,\n            children: \"Request Price\"\n          })\n        }), _jsx(TouchableOpacity, {\n          style: styles.detailsButton,\n          children: _jsx(Text, {\n            style: styles.detailsButtonText,\n            children: \"View Details\"\n          })\n        })]\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  card: {\n    backgroundColor: \"white\",\n    borderRadius: 12,\n    marginBottom: 16,\n    shadowColor: \"#000\",\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 3.84,\n    elevation: 5\n  },\n  image: {\n    width: \"100%\",\n    height: 200,\n    borderTopLeftRadius: 12,\n    borderTopRightRadius: 12\n  },\n  content: {\n    padding: 16\n  },\n  header: {\n    flexDirection: \"row\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    marginBottom: 8\n  },\n  name: {\n    fontSize: 18,\n    fontWeight: \"bold\",\n    color: \"#333\",\n    flex: 1\n  },\n  rating: {\n    flexDirection: \"row\",\n    alignItems: \"center\"\n  },\n  ratingText: {\n    marginLeft: 4,\n    fontSize: 16,\n    fontWeight: \"bold\",\n    color: \"#333\"\n  },\n  location: {\n    flexDirection: \"row\",\n    alignItems: \"center\",\n    marginBottom: 12\n  },\n  locationText: {\n    marginLeft: 4,\n    fontSize: 14,\n    color: \"#666\"\n  },\n  services: {\n    flexDirection: \"row\",\n    flexWrap: \"wrap\",\n    marginBottom: 12\n  },\n  serviceTag: {\n    backgroundColor: \"#E3F2FD\",\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    borderRadius: 16,\n    marginRight: 8,\n    marginBottom: 4\n  },\n  serviceText: {\n    fontSize: 12,\n    color: \"#4A90E2\",\n    fontWeight: \"500\"\n  },\n  description: {\n    fontSize: 14,\n    color: \"#666\",\n    lineHeight: 20,\n    marginBottom: 16\n  },\n  actions: {\n    flexDirection: \"row\",\n    justifyContent: \"space-between\"\n  },\n  requestButton: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: \"#4A90E2\",\n    borderRadius: 8,\n    paddingVertical: 12,\n    marginRight: 8,\n    alignItems: \"center\"\n  },\n  requestButtonText: {\n    color: \"#4A90E2\",\n    fontSize: 14,\n    fontWeight: \"600\"\n  },\n  detailsButton: {\n    flex: 1,\n    backgroundColor: \"#4A90E2\",\n    borderRadius: 8,\n    paddingVertical: 12,\n    marginLeft: 8,\n    alignItems: \"center\"\n  },\n  detailsButtonText: {\n    color: \"white\",\n    fontSize: 14,\n    fontWeight: \"600\"\n  }\n});\nexport default ServiceProviderCard;", "map": {"version": 3, "names": ["Icon", "jsx", "_jsx", "jsxs", "_jsxs", "ServiceProviderCard", "_ref", "provider", "View", "style", "styles", "card", "children", "Image", "source", "uri", "image", "content", "header", "Text", "name", "rating", "size", "color", "ratingText", "location", "locationText", "services", "map", "service", "index", "serviceTag", "serviceText", "description", "actions", "TouchableOpacity", "requestButton", "requestButtonText", "detailsButton", "detailsButtonText", "StyleSheet", "create", "backgroundColor", "borderRadius", "marginBottom", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "borderTopLeftRadius", "borderTopRightRadius", "padding", "flexDirection", "justifyContent", "alignItems", "fontSize", "fontWeight", "flex", "marginLeft", "flexWrap", "paddingHorizontal", "paddingVertical", "marginRight", "lineHeight", "borderWidth", "borderColor"], "sources": ["/Users/<USER>/Downloads/logistics-app/src/components/ServiceProviderCard.tsx"], "sourcesContent": ["import type React from \"react\"\nimport { View, Text, StyleSheet, TouchableOpacity, Image } from \"react-native\"\nimport Icon from \"./Icon\"\n\ninterface ServiceProvider {\n  id: number\n  name: string\n  location: string\n  rating: number\n  services: string[]\n  description: string\n  image: string\n}\n\ninterface ServiceProviderCardProps {\n  provider: ServiceProvider\n}\n\nconst ServiceProviderCard: React.FC<ServiceProviderCardProps> = ({ provider }) => {\n  return (\n    <View style={styles.card}>\n      <Image source={{ uri: provider.image }} style={styles.image} />\n\n      <View style={styles.content}>\n        <View style={styles.header}>\n          <Text style={styles.name}>{provider.name}</Text>\n          <View style={styles.rating}>\n            <Icon name=\"star\" size={16} color=\"#FFD700\" />\n            <Text style={styles.ratingText}>{provider.rating}</Text>\n          </View>\n        </View>\n\n        <View style={styles.location}>\n          <Icon name=\"map-pin\" size={16} color=\"#666\" />\n          <Text style={styles.locationText}>{provider.location}</Text>\n        </View>\n\n        <View style={styles.services}>\n          {provider.services.map((service, index) => (\n            <View key={index} style={styles.serviceTag}>\n              <Text style={styles.serviceText}>{service}</Text>\n            </View>\n          ))}\n        </View>\n\n        <Text style={styles.description}>{provider.description}</Text>\n\n        <View style={styles.actions}>\n          <TouchableOpacity style={styles.requestButton}>\n            <Text style={styles.requestButtonText}>Request Price</Text>\n          </TouchableOpacity>\n          <TouchableOpacity style={styles.detailsButton}>\n            <Text style={styles.detailsButtonText}>View Details</Text>\n          </TouchableOpacity>\n        </View>\n      </View>\n    </View>\n  )\n}\n\nconst styles = StyleSheet.create({\n  card: {\n    backgroundColor: \"white\",\n    borderRadius: 12,\n    marginBottom: 16,\n    shadowColor: \"#000\",\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 3.84,\n    elevation: 5,\n  },\n  image: {\n    width: \"100%\",\n    height: 200,\n    borderTopLeftRadius: 12,\n    borderTopRightRadius: 12,\n  },\n  content: {\n    padding: 16,\n  },\n  header: {\n    flexDirection: \"row\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    marginBottom: 8,\n  },\n  name: {\n    fontSize: 18,\n    fontWeight: \"bold\",\n    color: \"#333\",\n    flex: 1,\n  },\n  rating: {\n    flexDirection: \"row\",\n    alignItems: \"center\",\n  },\n  ratingText: {\n    marginLeft: 4,\n    fontSize: 16,\n    fontWeight: \"bold\",\n    color: \"#333\",\n  },\n  location: {\n    flexDirection: \"row\",\n    alignItems: \"center\",\n    marginBottom: 12,\n  },\n  locationText: {\n    marginLeft: 4,\n    fontSize: 14,\n    color: \"#666\",\n  },\n  services: {\n    flexDirection: \"row\",\n    flexWrap: \"wrap\",\n    marginBottom: 12,\n  },\n  serviceTag: {\n    backgroundColor: \"#E3F2FD\",\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    borderRadius: 16,\n    marginRight: 8,\n    marginBottom: 4,\n  },\n  serviceText: {\n    fontSize: 12,\n    color: \"#4A90E2\",\n    fontWeight: \"500\",\n  },\n  description: {\n    fontSize: 14,\n    color: \"#666\",\n    lineHeight: 20,\n    marginBottom: 16,\n  },\n  actions: {\n    flexDirection: \"row\",\n    justifyContent: \"space-between\",\n  },\n  requestButton: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: \"#4A90E2\",\n    borderRadius: 8,\n    paddingVertical: 12,\n    marginRight: 8,\n    alignItems: \"center\",\n  },\n  requestButtonText: {\n    color: \"#4A90E2\",\n    fontSize: 14,\n    fontWeight: \"600\",\n  },\n  detailsButton: {\n    flex: 1,\n    backgroundColor: \"#4A90E2\",\n    borderRadius: 8,\n    paddingVertical: 12,\n    marginLeft: 8,\n    alignItems: \"center\",\n  },\n  detailsButtonText: {\n    color: \"white\",\n    fontSize: 14,\n    fontWeight: \"600\",\n  },\n})\n\nexport default ServiceProviderCard\n"], "mappings": ";;;;;AAEA,OAAOA,IAAI;AAAc,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAgBzB,IAAMC,mBAAuD,GAAG,SAA1DA,mBAAuDA,CAAAC,IAAA,EAAqB;EAAA,IAAfC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EACzE,OACEH,KAAA,CAACI,IAAI;IAACC,KAAK,EAAEC,MAAM,CAACC,IAAK;IAAAC,QAAA,GACvBV,IAAA,CAACW,KAAK;MAACC,MAAM,EAAE;QAAEC,GAAG,EAAER,QAAQ,CAACS;MAAM,CAAE;MAACP,KAAK,EAAEC,MAAM,CAACM;IAAM,CAAE,CAAC,EAE/DZ,KAAA,CAACI,IAAI;MAACC,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAL,QAAA,GAC1BR,KAAA,CAACI,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACQ,MAAO;QAAAN,QAAA,GACzBV,IAAA,CAACiB,IAAI;UAACV,KAAK,EAAEC,MAAM,CAACU,IAAK;UAAAR,QAAA,EAAEL,QAAQ,CAACa;QAAI,CAAO,CAAC,EAChDhB,KAAA,CAACI,IAAI;UAACC,KAAK,EAAEC,MAAM,CAACW,MAAO;UAAAT,QAAA,GACzBV,IAAA,CAACF,IAAI;YAACoB,IAAI,EAAC,MAAM;YAACE,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS,CAAE,CAAC,EAC9CrB,IAAA,CAACiB,IAAI;YAACV,KAAK,EAAEC,MAAM,CAACc,UAAW;YAAAZ,QAAA,EAAEL,QAAQ,CAACc;UAAM,CAAO,CAAC;QAAA,CACpD,CAAC;MAAA,CACH,CAAC,EAEPjB,KAAA,CAACI,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACe,QAAS;QAAAb,QAAA,GAC3BV,IAAA,CAACF,IAAI;UAACoB,IAAI,EAAC,SAAS;UAACE,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAM,CAAE,CAAC,EAC9CrB,IAAA,CAACiB,IAAI;UAACV,KAAK,EAAEC,MAAM,CAACgB,YAAa;UAAAd,QAAA,EAAEL,QAAQ,CAACkB;QAAQ,CAAO,CAAC;MAAA,CACxD,CAAC,EAEPvB,IAAA,CAACM,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACiB,QAAS;QAAAf,QAAA,EAC1BL,QAAQ,CAACoB,QAAQ,CAACC,GAAG,CAAC,UAACC,OAAO,EAAEC,KAAK;UAAA,OACpC5B,IAAA,CAACM,IAAI;YAAaC,KAAK,EAAEC,MAAM,CAACqB,UAAW;YAAAnB,QAAA,EACzCV,IAAA,CAACiB,IAAI;cAACV,KAAK,EAAEC,MAAM,CAACsB,WAAY;cAAApB,QAAA,EAAEiB;YAAO,CAAO;UAAC,GADxCC,KAEL,CAAC;QAAA,CACR;MAAC,CACE,CAAC,EAEP5B,IAAA,CAACiB,IAAI;QAACV,KAAK,EAAEC,MAAM,CAACuB,WAAY;QAAArB,QAAA,EAAEL,QAAQ,CAAC0B;MAAW,CAAO,CAAC,EAE9D7B,KAAA,CAACI,IAAI;QAACC,KAAK,EAAEC,MAAM,CAACwB,OAAQ;QAAAtB,QAAA,GAC1BV,IAAA,CAACiC,gBAAgB;UAAC1B,KAAK,EAAEC,MAAM,CAAC0B,aAAc;UAAAxB,QAAA,EAC5CV,IAAA,CAACiB,IAAI;YAACV,KAAK,EAAEC,MAAM,CAAC2B,iBAAkB;YAAAzB,QAAA,EAAC;UAAa,CAAM;QAAC,CAC3C,CAAC,EACnBV,IAAA,CAACiC,gBAAgB;UAAC1B,KAAK,EAAEC,MAAM,CAAC4B,aAAc;UAAA1B,QAAA,EAC5CV,IAAA,CAACiB,IAAI;YAACV,KAAK,EAAEC,MAAM,CAAC6B,iBAAkB;YAAA3B,QAAA,EAAC;UAAY,CAAM;QAAC,CAC1C,CAAC;MAAA,CACf,CAAC;IAAA,CACH,CAAC;EAAA,CACH,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAG8B,UAAU,CAACC,MAAM,CAAC;EAC/B9B,IAAI,EAAE;IACJ+B,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE;EACb,CAAC;EACDnC,KAAK,EAAE;IACL+B,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,GAAG;IACXI,mBAAmB,EAAE,EAAE;IACvBC,oBAAoB,EAAE;EACxB,CAAC;EACDpC,OAAO,EAAE;IACPqC,OAAO,EAAE;EACX,CAAC;EACDpC,MAAM,EAAE;IACNqC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBb,YAAY,EAAE;EAChB,CAAC;EACDxB,IAAI,EAAE;IACJsC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBpC,KAAK,EAAE,MAAM;IACbqC,IAAI,EAAE;EACR,CAAC;EACDvC,MAAM,EAAE;IACNkC,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE;EACd,CAAC;EACDjC,UAAU,EAAE;IACVqC,UAAU,EAAE,CAAC;IACbH,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBpC,KAAK,EAAE;EACT,CAAC;EACDE,QAAQ,EAAE;IACR8B,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE,QAAQ;IACpBb,YAAY,EAAE;EAChB,CAAC;EACDlB,YAAY,EAAE;IACZmC,UAAU,EAAE,CAAC;IACbH,QAAQ,EAAE,EAAE;IACZnC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACR4B,aAAa,EAAE,KAAK;IACpBO,QAAQ,EAAE,MAAM;IAChBlB,YAAY,EAAE;EAChB,CAAC;EACDb,UAAU,EAAE;IACVW,eAAe,EAAE,SAAS;IAC1BqB,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,CAAC;IAClBrB,YAAY,EAAE,EAAE;IAChBsB,WAAW,EAAE,CAAC;IACdrB,YAAY,EAAE;EAChB,CAAC;EACDZ,WAAW,EAAE;IACX0B,QAAQ,EAAE,EAAE;IACZnC,KAAK,EAAE,SAAS;IAChBoC,UAAU,EAAE;EACd,CAAC;EACD1B,WAAW,EAAE;IACXyB,QAAQ,EAAE,EAAE;IACZnC,KAAK,EAAE,MAAM;IACb2C,UAAU,EAAE,EAAE;IACdtB,YAAY,EAAE;EAChB,CAAC;EACDV,OAAO,EAAE;IACPqB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDpB,aAAa,EAAE;IACbwB,IAAI,EAAE,CAAC;IACPO,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,SAAS;IACtBzB,YAAY,EAAE,CAAC;IACfqB,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,CAAC;IACdR,UAAU,EAAE;EACd,CAAC;EACDpB,iBAAiB,EAAE;IACjBd,KAAK,EAAE,SAAS;IAChBmC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDrB,aAAa,EAAE;IACbsB,IAAI,EAAE,CAAC;IACPlB,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,CAAC;IACfqB,eAAe,EAAE,EAAE;IACnBH,UAAU,EAAE,CAAC;IACbJ,UAAU,EAAE;EACd,CAAC;EACDlB,iBAAiB,EAAE;IACjBhB,KAAK,EAAE,OAAO;IACdmC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAetD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}