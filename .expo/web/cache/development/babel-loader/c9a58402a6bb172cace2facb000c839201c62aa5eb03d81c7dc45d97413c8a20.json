{"ast": null, "code": "import invariant from 'fbjs/lib/invariant';\nimport TaskQueue from \"./TaskQueue\";\nimport EventEmitter from \"../../vendor/react-native/vendor/emitter/EventEmitter\";\nimport requestIdleCallback from \"../../modules/requestIdleCallback\";\nvar _emitter = new EventEmitter();\nvar InteractionManager = {\n  Events: {\n    interactionStart: 'interactionStart',\n    interactionComplete: 'interactionComplete'\n  },\n  runAfterInteractions: function runAfterInteractions(task) {\n    var tasks = [];\n    var promise = new Promise(function (resolve) {\n      _scheduleUpdate();\n      if (task) {\n        tasks.push(task);\n      }\n      tasks.push({\n        run: resolve,\n        name: 'resolve ' + (task && task.name || '?')\n      });\n      _taskQueue.enqueueTasks(tasks);\n    });\n    return {\n      then: promise.then.bind(promise),\n      done: promise.then.bind(promise),\n      cancel: function cancel() {\n        _taskQueue.cancelTasks(tasks);\n      }\n    };\n  },\n  createInteractionHandle: function createInteractionHandle() {\n    _scheduleUpdate();\n    var handle = ++_inc;\n    _addInteractionSet.add(handle);\n    return handle;\n  },\n  clearInteractionHandle: function clearInteractionHandle(handle) {\n    invariant(!!handle, 'Must provide a handle to clear.');\n    _scheduleUpdate();\n    _addInteractionSet.delete(handle);\n    _deleteInteractionSet.add(handle);\n  },\n  addListener: _emitter.addListener.bind(_emitter),\n  setDeadline: function setDeadline(deadline) {\n    _deadline = deadline;\n  }\n};\nvar _interactionSet = new Set();\nvar _addInteractionSet = new Set();\nvar _deleteInteractionSet = new Set();\nvar _taskQueue = new TaskQueue({\n  onMoreTasks: _scheduleUpdate\n});\nvar _nextUpdateHandle = 0;\nvar _inc = 0;\nvar _deadline = -1;\nfunction _scheduleUpdate() {\n  if (!_nextUpdateHandle) {\n    if (_deadline > 0) {\n      _nextUpdateHandle = setTimeout(_processUpdate);\n    } else {\n      _nextUpdateHandle = requestIdleCallback(_processUpdate);\n    }\n  }\n}\nfunction _processUpdate() {\n  _nextUpdateHandle = 0;\n  var interactionCount = _interactionSet.size;\n  _addInteractionSet.forEach(function (handle) {\n    return _interactionSet.add(handle);\n  });\n  _deleteInteractionSet.forEach(function (handle) {\n    return _interactionSet.delete(handle);\n  });\n  var nextInteractionCount = _interactionSet.size;\n  if (interactionCount !== 0 && nextInteractionCount === 0) {\n    _emitter.emit(InteractionManager.Events.interactionComplete);\n  } else if (interactionCount === 0 && nextInteractionCount !== 0) {\n    _emitter.emit(InteractionManager.Events.interactionStart);\n  }\n  if (nextInteractionCount === 0) {\n    var begin = Date.now();\n    while (_taskQueue.hasTasksToProcess()) {\n      _taskQueue.processNext();\n      if (_deadline > 0 && Date.now() - begin >= _deadline) {\n        _scheduleUpdate();\n        break;\n      }\n    }\n  }\n  _addInteractionSet.clear();\n  _deleteInteractionSet.clear();\n}\nexport default InteractionManager;", "map": {"version": 3, "names": ["invariant", "TaskQueue", "EventEmitter", "requestIdleCallback", "_emitter", "InteractionManager", "Events", "interactionStart", "interactionComplete", "runAfterInteractions", "task", "tasks", "promise", "Promise", "resolve", "_scheduleUpdate", "push", "run", "name", "_taskQueue", "enqueueTasks", "then", "bind", "done", "cancel", "cancelTasks", "createInteractionHandle", "handle", "_inc", "_addInteractionSet", "add", "clearInteractionHandle", "delete", "_deleteInteractionSet", "addListener", "setDeadline", "deadline", "_deadline", "_interactionSet", "Set", "onMoreTasks", "_nextUpdate<PERSON><PERSON>le", "setTimeout", "_processUpdate", "interactionCount", "size", "for<PERSON>ach", "nextInteractionCount", "emit", "begin", "Date", "now", "hasTasksToProcess", "processNext", "clear"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/exports/InteractionManager/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport invariant from 'fbjs/lib/invariant';\nimport TaskQueue from './TaskQueue';\nimport EventEmitter from '../../vendor/react-native/vendor/emitter/EventEmitter';\nimport requestIdleCallback from '../../modules/requestIdleCallback';\nvar _emitter = new EventEmitter();\nvar InteractionManager = {\n  Events: {\n    interactionStart: 'interactionStart',\n    interactionComplete: 'interactionComplete'\n  },\n  /**\n   * Schedule a function to run after all interactions have completed.\n   */\n  runAfterInteractions(task) {\n    var tasks = [];\n    var promise = new Promise(resolve => {\n      _scheduleUpdate();\n      if (task) {\n        tasks.push(task);\n      }\n      tasks.push({\n        run: resolve,\n        name: 'resolve ' + (task && task.name || '?')\n      });\n      _taskQueue.enqueueTasks(tasks);\n    });\n    return {\n      then: promise.then.bind(promise),\n      done: promise.then.bind(promise),\n      cancel: () => {\n        _taskQueue.cancelTasks(tasks);\n      }\n    };\n  },\n  /**\n   * Notify manager that an interaction has started.\n   */\n  createInteractionHandle() {\n    _scheduleUpdate();\n    var handle = ++_inc;\n    _addInteractionSet.add(handle);\n    return handle;\n  },\n  /**\n   * Notify manager that an interaction has completed.\n   */\n  clearInteractionHandle(handle) {\n    invariant(!!handle, 'Must provide a handle to clear.');\n    _scheduleUpdate();\n    _addInteractionSet.delete(handle);\n    _deleteInteractionSet.add(handle);\n  },\n  addListener: _emitter.addListener.bind(_emitter),\n  /**\n   *\n   * @param deadline\n   */\n  setDeadline(deadline) {\n    _deadline = deadline;\n  }\n};\nvar _interactionSet = new Set();\nvar _addInteractionSet = new Set();\nvar _deleteInteractionSet = new Set();\nvar _taskQueue = new TaskQueue({\n  onMoreTasks: _scheduleUpdate\n});\nvar _nextUpdateHandle = 0;\nvar _inc = 0;\nvar _deadline = -1;\n\n/**\n * Schedule an asynchronous update to the interaction state.\n */\nfunction _scheduleUpdate() {\n  if (!_nextUpdateHandle) {\n    if (_deadline > 0) {\n      _nextUpdateHandle = setTimeout(_processUpdate);\n    } else {\n      _nextUpdateHandle = requestIdleCallback(_processUpdate);\n    }\n  }\n}\n\n/**\n * Notify listeners, process queue, etc\n */\nfunction _processUpdate() {\n  _nextUpdateHandle = 0;\n  var interactionCount = _interactionSet.size;\n  _addInteractionSet.forEach(handle => _interactionSet.add(handle));\n  _deleteInteractionSet.forEach(handle => _interactionSet.delete(handle));\n  var nextInteractionCount = _interactionSet.size;\n  if (interactionCount !== 0 && nextInteractionCount === 0) {\n    _emitter.emit(InteractionManager.Events.interactionComplete);\n  } else if (interactionCount === 0 && nextInteractionCount !== 0) {\n    _emitter.emit(InteractionManager.Events.interactionStart);\n  }\n  if (nextInteractionCount === 0) {\n    // It seems that we can't know the running time of the current event loop,\n    // we can only calculate the running time of the current task queue.\n    var begin = Date.now();\n    while (_taskQueue.hasTasksToProcess()) {\n      _taskQueue.processNext();\n      if (_deadline > 0 && Date.now() - begin >= _deadline) {\n        _scheduleUpdate();\n        break;\n      }\n    }\n  }\n  _addInteractionSet.clear();\n  _deleteInteractionSet.clear();\n}\nexport default InteractionManager;"], "mappings": "AAUA,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS;AAChB,OAAOC,YAAY;AACnB,OAAOC,mBAAmB;AAC1B,IAAIC,QAAQ,GAAG,IAAIF,YAAY,CAAC,CAAC;AACjC,IAAIG,kBAAkB,GAAG;EACvBC,MAAM,EAAE;IACNC,gBAAgB,EAAE,kBAAkB;IACpCC,mBAAmB,EAAE;EACvB,CAAC;EAIDC,oBAAoB,WAApBA,oBAAoBA,CAACC,IAAI,EAAE;IACzB,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MACnCC,eAAe,CAAC,CAAC;MACjB,IAAIL,IAAI,EAAE;QACRC,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;MAClB;MACAC,KAAK,CAACK,IAAI,CAAC;QACTC,GAAG,EAAEH,OAAO;QACZI,IAAI,EAAE,UAAU,IAAIR,IAAI,IAAIA,IAAI,CAACQ,IAAI,IAAI,GAAG;MAC9C,CAAC,CAAC;MACFC,UAAU,CAACC,YAAY,CAACT,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,OAAO;MACLU,IAAI,EAAET,OAAO,CAACS,IAAI,CAACC,IAAI,CAACV,OAAO,CAAC;MAChCW,IAAI,EAAEX,OAAO,CAACS,IAAI,CAACC,IAAI,CAACV,OAAO,CAAC;MAChCY,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;QACZL,UAAU,CAACM,WAAW,CAACd,KAAK,CAAC;MAC/B;IACF,CAAC;EACH,CAAC;EAIDe,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;IACxBX,eAAe,CAAC,CAAC;IACjB,IAAIY,MAAM,GAAG,EAAEC,IAAI;IACnBC,kBAAkB,CAACC,GAAG,CAACH,MAAM,CAAC;IAC9B,OAAOA,MAAM;EACf,CAAC;EAIDI,sBAAsB,WAAtBA,sBAAsBA,CAACJ,MAAM,EAAE;IAC7B3B,SAAS,CAAC,CAAC,CAAC2B,MAAM,EAAE,iCAAiC,CAAC;IACtDZ,eAAe,CAAC,CAAC;IACjBc,kBAAkB,CAACG,MAAM,CAACL,MAAM,CAAC;IACjCM,qBAAqB,CAACH,GAAG,CAACH,MAAM,CAAC;EACnC,CAAC;EACDO,WAAW,EAAE9B,QAAQ,CAAC8B,WAAW,CAACZ,IAAI,CAAClB,QAAQ,CAAC;EAKhD+B,WAAW,WAAXA,WAAWA,CAACC,QAAQ,EAAE;IACpBC,SAAS,GAAGD,QAAQ;EACtB;AACF,CAAC;AACD,IAAIE,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,IAAIV,kBAAkB,GAAG,IAAIU,GAAG,CAAC,CAAC;AAClC,IAAIN,qBAAqB,GAAG,IAAIM,GAAG,CAAC,CAAC;AACrC,IAAIpB,UAAU,GAAG,IAAIlB,SAAS,CAAC;EAC7BuC,WAAW,EAAEzB;AACf,CAAC,CAAC;AACF,IAAI0B,iBAAiB,GAAG,CAAC;AACzB,IAAIb,IAAI,GAAG,CAAC;AACZ,IAAIS,SAAS,GAAG,CAAC,CAAC;AAKlB,SAAStB,eAAeA,CAAA,EAAG;EACzB,IAAI,CAAC0B,iBAAiB,EAAE;IACtB,IAAIJ,SAAS,GAAG,CAAC,EAAE;MACjBI,iBAAiB,GAAGC,UAAU,CAACC,cAAc,CAAC;IAChD,CAAC,MAAM;MACLF,iBAAiB,GAAGtC,mBAAmB,CAACwC,cAAc,CAAC;IACzD;EACF;AACF;AAKA,SAASA,cAAcA,CAAA,EAAG;EACxBF,iBAAiB,GAAG,CAAC;EACrB,IAAIG,gBAAgB,GAAGN,eAAe,CAACO,IAAI;EAC3ChB,kBAAkB,CAACiB,OAAO,CAAC,UAAAnB,MAAM;IAAA,OAAIW,eAAe,CAACR,GAAG,CAACH,MAAM,CAAC;EAAA,EAAC;EACjEM,qBAAqB,CAACa,OAAO,CAAC,UAAAnB,MAAM;IAAA,OAAIW,eAAe,CAACN,MAAM,CAACL,MAAM,CAAC;EAAA,EAAC;EACvE,IAAIoB,oBAAoB,GAAGT,eAAe,CAACO,IAAI;EAC/C,IAAID,gBAAgB,KAAK,CAAC,IAAIG,oBAAoB,KAAK,CAAC,EAAE;IACxD3C,QAAQ,CAAC4C,IAAI,CAAC3C,kBAAkB,CAACC,MAAM,CAACE,mBAAmB,CAAC;EAC9D,CAAC,MAAM,IAAIoC,gBAAgB,KAAK,CAAC,IAAIG,oBAAoB,KAAK,CAAC,EAAE;IAC/D3C,QAAQ,CAAC4C,IAAI,CAAC3C,kBAAkB,CAACC,MAAM,CAACC,gBAAgB,CAAC;EAC3D;EACA,IAAIwC,oBAAoB,KAAK,CAAC,EAAE;IAG9B,IAAIE,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,OAAOhC,UAAU,CAACiC,iBAAiB,CAAC,CAAC,EAAE;MACrCjC,UAAU,CAACkC,WAAW,CAAC,CAAC;MACxB,IAAIhB,SAAS,GAAG,CAAC,IAAIa,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,IAAIZ,SAAS,EAAE;QACpDtB,eAAe,CAAC,CAAC;QACjB;MACF;IACF;EACF;EACAc,kBAAkB,CAACyB,KAAK,CAAC,CAAC;EAC1BrB,qBAAqB,CAACqB,KAAK,CAAC,CAAC;AAC/B;AACA,eAAejD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}