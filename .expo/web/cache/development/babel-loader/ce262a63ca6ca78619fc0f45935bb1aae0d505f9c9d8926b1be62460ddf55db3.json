{"ast": null, "code": "import isDisabled from \"./isDisabled\";\nimport propsToAccessibilityComponent from \"./propsToAccessibilityComponent\";\nimport propsToAriaRole from \"./propsToAriaRole\";\nvar AccessibilityUtil = {\n  isDisabled: isDisabled,\n  propsToAccessibilityComponent: propsToAccessibilityComponent,\n  propsToAriaRole: propsToAriaRole\n};\nexport default AccessibilityUtil;", "map": {"version": 3, "names": ["isDisabled", "propsToAccessibilityComponent", "propsToAriaRole", "AccessibilityUtil"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/modules/AccessibilityUtil/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport isDisabled from './isDisabled';\nimport propsToAccessibilityComponent from './propsToAccessibilityComponent';\nimport propsToAriaRole from './propsToAriaRole';\nvar AccessibilityUtil = {\n  isDisabled,\n  propsToAccessibilityComponent,\n  propsToAriaRole\n};\nexport default AccessibilityUtil;"], "mappings": "AASA,OAAOA,UAAU;AACjB,OAAOC,6BAA6B;AACpC,OAAOC,eAAe;AACtB,IAAIC,iBAAiB,GAAG;EACtBH,UAAU,EAAVA,UAAU;EACVC,6BAA6B,EAA7BA,6BAA6B;EAC7BC,eAAe,EAAfA;AACF,CAAC;AACD,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}