{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nvar _excluded = [\"numColumns\", \"columnWrapperStyle\", \"removeClippedSubviews\", \"strictMode\"];\nimport View from \"../../../exports/View\";\nimport StyleSheet from \"../../../exports/StyleSheet\";\nimport deepDiffer from \"../deepDiffer\";\nimport Platform from \"../../../exports/Platform\";\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nimport VirtualizedList from \"../VirtualizedList\";\nimport { keyExtractor as defaultKeyExtractor } from \"../VirtualizeUtils\";\nimport memoizeOne from 'memoize-one';\nfunction removeClippedSubviewsOrDefault(removeClippedSubviews) {\n  return removeClippedSubviews !== null && removeClippedSubviews !== void 0 ? removeClippedSubviews : Platform.OS === 'android';\n}\nfunction numColumnsOrDefault(numColumns) {\n  return numColumns !== null && numColumns !== void 0 ? numColumns : 1;\n}\nfunction isArrayLike(data) {\n  return typeof Object(data).length === 'number';\n}\nvar FlatList = function (_React$PureComponent) {\n  function FlatList(_props) {\n    var _this;\n    _classCallCheck(this, FlatList);\n    _this = _callSuper(this, FlatList, [_props]);\n    _this._virtualizedListPairs = [];\n    _this._captureRef = function (ref) {\n      _this._listRef = ref;\n    };\n    _this._getItem = function (data, index) {\n      var numColumns = numColumnsOrDefault(_this.props.numColumns);\n      if (numColumns > 1) {\n        var ret = [];\n        for (var kk = 0; kk < numColumns; kk++) {\n          var itemIndex = index * numColumns + kk;\n          if (itemIndex < data.length) {\n            var _item = data[itemIndex];\n            ret.push(_item);\n          }\n        }\n        return ret;\n      } else {\n        return data[index];\n      }\n    };\n    _this._getItemCount = function (data) {\n      if (data != null && isArrayLike(data)) {\n        var numColumns = numColumnsOrDefault(_this.props.numColumns);\n        return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;\n      } else {\n        return 0;\n      }\n    };\n    _this._keyExtractor = function (items, index) {\n      var _this$props$keyExtrac;\n      var numColumns = numColumnsOrDefault(_this.props.numColumns);\n      var keyExtractor = (_this$props$keyExtrac = _this.props.keyExtractor) !== null && _this$props$keyExtrac !== void 0 ? _this$props$keyExtrac : defaultKeyExtractor;\n      if (numColumns > 1) {\n        invariant(Array.isArray(items), 'FlatList: Encountered internal consistency error, expected each item to consist of an ' + 'array with 1-%s columns; instead, received a single item.', numColumns);\n        return items.map(function (item, kk) {\n          return keyExtractor(item, index * numColumns + kk);\n        }).join(':');\n      }\n      return keyExtractor(items, index);\n    };\n    _this._renderer = function (ListItemComponent, renderItem, columnWrapperStyle, numColumns, extraData) {\n      var cols = numColumnsOrDefault(numColumns);\n      var render = function render(props) {\n        if (ListItemComponent) {\n          return React.createElement(ListItemComponent, props);\n        } else if (renderItem) {\n          return renderItem(props);\n        } else {\n          return null;\n        }\n      };\n      var renderProp = function renderProp(info) {\n        if (cols > 1) {\n          var _item2 = info.item,\n            _index = info.index;\n          invariant(Array.isArray(_item2), 'Expected array of items with numColumns > 1');\n          return React.createElement(View, {\n            style: [styles.row, columnWrapperStyle]\n          }, _item2.map(function (it, kk) {\n            var element = render({\n              item: it,\n              index: _index * cols + kk,\n              separators: info.separators\n            });\n            return element != null ? React.createElement(React.Fragment, {\n              key: kk\n            }, element) : null;\n          }));\n        } else {\n          return render(info);\n        }\n      };\n      return ListItemComponent ? {\n        ListItemComponent: renderProp\n      } : {\n        renderItem: renderProp\n      };\n    };\n    _this._memoizedRenderer = memoizeOne(_this._renderer);\n    _this._checkProps(_this.props);\n    if (_this.props.viewabilityConfigCallbackPairs) {\n      _this._virtualizedListPairs = _this.props.viewabilityConfigCallbackPairs.map(function (pair) {\n        return {\n          viewabilityConfig: pair.viewabilityConfig,\n          onViewableItemsChanged: _this._createOnViewableItemsChanged(pair.onViewableItemsChanged)\n        };\n      });\n    } else if (_this.props.onViewableItemsChanged) {\n      _this._virtualizedListPairs.push({\n        viewabilityConfig: _this.props.viewabilityConfig,\n        onViewableItemsChanged: _this._createOnViewableItemsChanged(_this.props.onViewableItemsChanged)\n      });\n    }\n    return _this;\n  }\n  _inherits(FlatList, _React$PureComponent);\n  return _createClass(FlatList, [{\n    key: \"scrollToEnd\",\n    value: function scrollToEnd(params) {\n      if (this._listRef) {\n        this._listRef.scrollToEnd(params);\n      }\n    }\n  }, {\n    key: \"scrollToIndex\",\n    value: function scrollToIndex(params) {\n      if (this._listRef) {\n        this._listRef.scrollToIndex(params);\n      }\n    }\n  }, {\n    key: \"scrollToItem\",\n    value: function scrollToItem(params) {\n      if (this._listRef) {\n        this._listRef.scrollToItem(params);\n      }\n    }\n  }, {\n    key: \"scrollToOffset\",\n    value: function scrollToOffset(params) {\n      if (this._listRef) {\n        this._listRef.scrollToOffset(params);\n      }\n    }\n  }, {\n    key: \"recordInteraction\",\n    value: function recordInteraction() {\n      if (this._listRef) {\n        this._listRef.recordInteraction();\n      }\n    }\n  }, {\n    key: \"flashScrollIndicators\",\n    value: function flashScrollIndicators() {\n      if (this._listRef) {\n        this._listRef.flashScrollIndicators();\n      }\n    }\n  }, {\n    key: \"getScrollResponder\",\n    value: function getScrollResponder() {\n      if (this._listRef) {\n        return this._listRef.getScrollResponder();\n      }\n    }\n  }, {\n    key: \"getNativeScrollRef\",\n    value: function getNativeScrollRef() {\n      if (this._listRef) {\n        return this._listRef.getScrollRef();\n      }\n    }\n  }, {\n    key: \"getScrollableNode\",\n    value: function getScrollableNode() {\n      if (this._listRef) {\n        return this._listRef.getScrollableNode();\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      invariant(prevProps.numColumns === this.props.numColumns, 'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' + 'changing the number of columns to force a fresh render of the component.');\n      invariant(prevProps.onViewableItemsChanged === this.props.onViewableItemsChanged, 'Changing onViewableItemsChanged on the fly is not supported');\n      invariant(!deepDiffer(prevProps.viewabilityConfig, this.props.viewabilityConfig), 'Changing viewabilityConfig on the fly is not supported');\n      invariant(prevProps.viewabilityConfigCallbackPairs === this.props.viewabilityConfigCallbackPairs, 'Changing viewabilityConfigCallbackPairs on the fly is not supported');\n      this._checkProps(this.props);\n    }\n  }, {\n    key: \"_checkProps\",\n    value: function _checkProps(props) {\n      var getItem = props.getItem,\n        getItemCount = props.getItemCount,\n        horizontal = props.horizontal,\n        columnWrapperStyle = props.columnWrapperStyle,\n        onViewableItemsChanged = props.onViewableItemsChanged,\n        viewabilityConfigCallbackPairs = props.viewabilityConfigCallbackPairs;\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      invariant(!getItem && !getItemCount, 'FlatList does not support custom data formats.');\n      if (numColumns > 1) {\n        invariant(!horizontal, 'numColumns does not support horizontal.');\n      } else {\n        invariant(!columnWrapperStyle, 'columnWrapperStyle not supported for single column lists');\n      }\n      invariant(!(onViewableItemsChanged && viewabilityConfigCallbackPairs), 'FlatList does not support setting both onViewableItemsChanged and ' + 'viewabilityConfigCallbackPairs.');\n    }\n  }, {\n    key: \"_pushMultiColumnViewable\",\n    value: function _pushMultiColumnViewable(arr, v) {\n      var _this$props$keyExtrac2;\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      var keyExtractor = (_this$props$keyExtrac2 = this.props.keyExtractor) !== null && _this$props$keyExtrac2 !== void 0 ? _this$props$keyExtrac2 : defaultKeyExtractor;\n      v.item.forEach(function (item, ii) {\n        invariant(v.index != null, 'Missing index!');\n        var index = v.index * numColumns + ii;\n        arr.push(_objectSpread(_objectSpread({}, v), {}, {\n          item: item,\n          key: keyExtractor(item, index),\n          index: index\n        }));\n      });\n    }\n  }, {\n    key: \"_createOnViewableItemsChanged\",\n    value: function _createOnViewableItemsChanged(onViewableItemsChanged) {\n      var _this2 = this;\n      return function (info) {\n        var numColumns = numColumnsOrDefault(_this2.props.numColumns);\n        if (onViewableItemsChanged) {\n          if (numColumns > 1) {\n            var changed = [];\n            var viewableItems = [];\n            info.viewableItems.forEach(function (v) {\n              return _this2._pushMultiColumnViewable(viewableItems, v);\n            });\n            info.changed.forEach(function (v) {\n              return _this2._pushMultiColumnViewable(changed, v);\n            });\n            onViewableItemsChanged({\n              viewableItems: viewableItems,\n              changed: changed\n            });\n          } else {\n            onViewableItemsChanged(info);\n          }\n        }\n      };\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        numColumns = _this$props.numColumns,\n        columnWrapperStyle = _this$props.columnWrapperStyle,\n        _removeClippedSubviews = _this$props.removeClippedSubviews,\n        _this$props$strictMod = _this$props.strictMode,\n        strictMode = _this$props$strictMod === void 0 ? false : _this$props$strictMod,\n        restProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n      var renderer = strictMode ? this._memoizedRenderer : this._renderer;\n      return (React.createElement(VirtualizedList, _extends({}, restProps, {\n          getItem: this._getItem,\n          getItemCount: this._getItemCount,\n          keyExtractor: this._keyExtractor,\n          ref: this._captureRef,\n          viewabilityConfigCallbackPairs: this._virtualizedListPairs,\n          removeClippedSubviews: removeClippedSubviewsOrDefault(_removeClippedSubviews)\n        }, renderer(this.props.ListItemComponent, this.props.renderItem, columnWrapperStyle, numColumns, this.props.extraData)))\n      );\n    }\n  }]);\n}(React.PureComponent);\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  }\n});\nexport default FlatList;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_objectSpread", "_excluded", "View", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON>", "Platform", "invariant", "React", "VirtualizedList", "keyExtractor", "defaultKeyExtractor", "memoizeOne", "removeClippedSubviewsOrDefault", "removeClippedSubviews", "OS", "numColumnsOrDefault", "numColumns", "isArrayLike", "data", "Object", "length", "FlatList", "_React$PureComponent", "_props", "_this", "_classCallCheck", "_callSuper", "_virtualizedListPairs", "_captureRef", "ref", "_listRef", "_getItem", "index", "props", "ret", "kk", "itemIndex", "_item", "push", "_getItemCount", "Math", "ceil", "_keyExtractor", "items", "_this$props$keyExtrac", "Array", "isArray", "map", "item", "join", "_renderer", "ListItemComponent", "renderItem", "columnWrapperStyle", "extraData", "cols", "render", "createElement", "renderProp", "info", "_item2", "_index", "style", "styles", "row", "it", "element", "separators", "Fragment", "key", "_memoized<PERSON><PERSON><PERSON>", "_checkProps", "viewabilityConfigCallbackPairs", "pair", "viewabilityConfig", "onViewableItemsChanged", "_createOnViewableItemsChanged", "_inherits", "_createClass", "value", "scrollToEnd", "params", "scrollToIndex", "scrollToItem", "scrollToOffset", "recordInteraction", "flashScrollIndicators", "getScrollResponder", "getNativeScrollRef", "getScrollRef", "getScrollableNode", "componentDidUpdate", "prevProps", "getItem", "getItemCount", "horizontal", "_pushMultiColumnViewable", "arr", "v", "_this$props$keyExtrac2", "for<PERSON>ach", "ii", "_this2", "changed", "viewableItems", "_this$props", "_removeClippedSubviews", "_this$props$strictMod", "strictMode", "restProps", "renderer", "PureComponent", "create", "flexDirection"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/FlatList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nvar _excluded = [\"numColumns\", \"columnWrapperStyle\", \"removeClippedSubviews\", \"strictMode\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport View from '../../../exports/View';\nimport StyleSheet from '../../../exports/StyleSheet';\nimport deepDiffer from '../deepDiffer';\nimport Platform from '../../../exports/Platform';\nimport invariant from 'fbjs/lib/invariant';\nimport * as React from 'react';\nimport VirtualizedList from '../VirtualizedList';\nimport { keyExtractor as defaultKeyExtractor } from '../VirtualizeUtils';\nimport memoizeOne from 'memoize-one';\n/**\n * Default Props Helper Functions\n * Use the following helper functions for default values\n */\n\n// removeClippedSubviewsOrDefault(this.props.removeClippedSubviews)\nfunction removeClippedSubviewsOrDefault(removeClippedSubviews) {\n  return removeClippedSubviews !== null && removeClippedSubviews !== void 0 ? removeClippedSubviews : Platform.OS === 'android';\n}\n\n// numColumnsOrDefault(this.props.numColumns)\nfunction numColumnsOrDefault(numColumns) {\n  return numColumns !== null && numColumns !== void 0 ? numColumns : 1;\n}\nfunction isArrayLike(data) {\n  // $FlowExpectedError[incompatible-use]\n  return typeof Object(data).length === 'number';\n}\n/**\n * A performant interface for rendering simple, flat lists, supporting the most handy features:\n *\n *  - Fully cross-platform.\n *  - Optional horizontal mode.\n *  - Configurable viewability callbacks.\n *  - Header support.\n *  - Footer support.\n *  - Separator support.\n *  - Pull to Refresh.\n *  - Scroll loading.\n *  - ScrollToIndex support.\n *\n * If you need section support, use [`<SectionList>`](docs/sectionlist.html).\n *\n * Minimal Example:\n *\n *     <FlatList\n *       data={[{key: 'a'}, {key: 'b'}]}\n *       renderItem={({item}) => <Text>{item.key}</Text>}\n *     />\n *\n * More complex, multi-select example demonstrating `PureComponent` usage for perf optimization and avoiding bugs.\n *\n * - By binding the `onPressItem` handler, the props will remain `===` and `PureComponent` will\n *   prevent wasteful re-renders unless the actual `id`, `selected`, or `title` props change, even\n *   if the components rendered in `MyListItem` did not have such optimizations.\n * - By passing `extraData={this.state}` to `FlatList` we make sure `FlatList` itself will re-render\n *   when the `state.selected` changes. Without setting this prop, `FlatList` would not know it\n *   needs to re-render any items because it is also a `PureComponent` and the prop comparison will\n *   not show any changes.\n * - `keyExtractor` tells the list to use the `id`s for the react keys instead of the default `key` property.\n *\n *\n *     class MyListItem extends React.PureComponent {\n *       _onPress = () => {\n *         this.props.onPressItem(this.props.id);\n *       };\n *\n *       render() {\n *         const textColor = this.props.selected ? \"red\" : \"black\";\n *         return (\n *           <TouchableOpacity onPress={this._onPress}>\n *             <View>\n *               <Text style={{ color: textColor }}>\n *                 {this.props.title}\n *               </Text>\n *             </View>\n *           </TouchableOpacity>\n *         );\n *       }\n *     }\n *\n *     class MultiSelectList extends React.PureComponent {\n *       state = {selected: (new Map(): Map<string, boolean>)};\n *\n *       _keyExtractor = (item, index) => item.id;\n *\n *       _onPressItem = (id: string) => {\n *         // updater functions are preferred for transactional updates\n *         this.setState((state) => {\n *           // copy the map rather than modifying state.\n *           const selected = new Map(state.selected);\n *           selected.set(id, !selected.get(id)); // toggle\n *           return {selected};\n *         });\n *       };\n *\n *       _renderItem = ({item}) => (\n *         <MyListItem\n *           id={item.id}\n *           onPressItem={this._onPressItem}\n *           selected={!!this.state.selected.get(item.id)}\n *           title={item.title}\n *         />\n *       );\n *\n *       render() {\n *         return (\n *           <FlatList\n *             data={this.props.data}\n *             extraData={this.state}\n *             keyExtractor={this._keyExtractor}\n *             renderItem={this._renderItem}\n *           />\n *         );\n *       }\n *     }\n *\n * This is a convenience wrapper around [`<VirtualizedList>`](docs/virtualizedlist.html),\n * and thus inherits its props (as well as those of `ScrollView`) that aren't explicitly listed\n * here, along with the following caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate ands momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n *\n * Also inherits [ScrollView Props](docs/scrollview.html#props), unless it is nested in another FlatList of same orientation.\n */\nclass FlatList extends React.PureComponent {\n  /**\n   * Scrolls to the end of the content. May be janky without `getItemLayout` prop.\n   */\n  scrollToEnd(params) {\n    if (this._listRef) {\n      this._listRef.scrollToEnd(params);\n    }\n  }\n\n  /**\n   * Scrolls to the item at the specified index such that it is positioned in the viewable area\n   * such that `viewPosition` 0 places it at the top, 1 at the bottom, and 0.5 centered in the\n   * middle. `viewOffset` is a fixed number of pixels to offset the final target position.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToIndex(params) {\n    if (this._listRef) {\n      this._listRef.scrollToIndex(params);\n    }\n  }\n\n  /**\n   * Requires linear scan through data - use `scrollToIndex` instead if possible.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToItem(params) {\n    if (this._listRef) {\n      this._listRef.scrollToItem(params);\n    }\n  }\n\n  /**\n   * Scroll to a specific content pixel offset in the list.\n   *\n   * Check out [scrollToOffset](docs/virtualizedlist.html#scrolltooffset) of VirtualizedList\n   */\n  scrollToOffset(params) {\n    if (this._listRef) {\n      this._listRef.scrollToOffset(params);\n    }\n  }\n\n  /**\n   * Tells the list an interaction has occurred, which should trigger viewability calculations, e.g.\n   * if `waitForInteractions` is true and the user has not scrolled. This is typically called by\n   * taps on items or by navigation actions.\n   */\n  recordInteraction() {\n    if (this._listRef) {\n      this._listRef.recordInteraction();\n    }\n  }\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators() {\n    if (this._listRef) {\n      this._listRef.flashScrollIndicators();\n    }\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   */\n  getScrollResponder() {\n    if (this._listRef) {\n      return this._listRef.getScrollResponder();\n    }\n  }\n\n  /**\n   * Provides a reference to the underlying host component\n   */\n  getNativeScrollRef() {\n    if (this._listRef) {\n      /* $FlowFixMe[incompatible-return] Suppresses errors found when fixing\n       * TextInput typing */\n      return this._listRef.getScrollRef();\n    }\n  }\n  getScrollableNode() {\n    if (this._listRef) {\n      return this._listRef.getScrollableNode();\n    }\n  }\n  constructor(_props) {\n    super(_props);\n    this._virtualizedListPairs = [];\n    this._captureRef = ref => {\n      this._listRef = ref;\n    };\n    this._getItem = (data, index) => {\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      if (numColumns > 1) {\n        var ret = [];\n        for (var kk = 0; kk < numColumns; kk++) {\n          var itemIndex = index * numColumns + kk;\n          if (itemIndex < data.length) {\n            var _item = data[itemIndex];\n            ret.push(_item);\n          }\n        }\n        return ret;\n      } else {\n        return data[index];\n      }\n    };\n    this._getItemCount = data => {\n      // Legacy behavior of FlatList was to forward \"undefined\" length if invalid\n      // data like a non-arraylike object is passed. VirtualizedList would then\n      // coerce this, and the math would work out to no-op. For compatibility, if\n      // invalid data is passed, we tell VirtualizedList there are zero items\n      // available to prevent it from trying to read from the invalid data\n      // (without propagating invalidly typed data).\n      if (data != null && isArrayLike(data)) {\n        var numColumns = numColumnsOrDefault(this.props.numColumns);\n        return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;\n      } else {\n        return 0;\n      }\n    };\n    this._keyExtractor = (items, index) => {\n      var _this$props$keyExtrac;\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      var keyExtractor = (_this$props$keyExtrac = this.props.keyExtractor) !== null && _this$props$keyExtrac !== void 0 ? _this$props$keyExtrac : defaultKeyExtractor;\n      if (numColumns > 1) {\n        invariant(Array.isArray(items), 'FlatList: Encountered internal consistency error, expected each item to consist of an ' + 'array with 1-%s columns; instead, received a single item.', numColumns);\n        return items.map((item, kk) => keyExtractor(item, index * numColumns + kk)).join(':');\n      }\n\n      // $FlowFixMe[incompatible-call] Can't call keyExtractor with an array\n      return keyExtractor(items, index);\n    };\n    this._renderer = (ListItemComponent, renderItem, columnWrapperStyle, numColumns, extraData\n    // $FlowFixMe[missing-local-annot]\n    ) => {\n      var cols = numColumnsOrDefault(numColumns);\n      var render = props => {\n        if (ListItemComponent) {\n          // $FlowFixMe[not-a-component] Component isn't valid\n          // $FlowFixMe[incompatible-type-arg] Component isn't valid\n          // $FlowFixMe[incompatible-return] Component isn't valid\n          return /*#__PURE__*/React.createElement(ListItemComponent, props);\n        } else if (renderItem) {\n          // $FlowFixMe[incompatible-call]\n          return renderItem(props);\n        } else {\n          return null;\n        }\n      };\n      var renderProp = info => {\n        if (cols > 1) {\n          var _item2 = info.item,\n            _index = info.index;\n          invariant(Array.isArray(_item2), 'Expected array of items with numColumns > 1');\n          return /*#__PURE__*/React.createElement(View, {\n            style: [styles.row, columnWrapperStyle]\n          }, _item2.map((it, kk) => {\n            var element = render({\n              // $FlowFixMe[incompatible-call]\n              item: it,\n              index: _index * cols + kk,\n              separators: info.separators\n            });\n            return element != null ? /*#__PURE__*/React.createElement(React.Fragment, {\n              key: kk\n            }, element) : null;\n          }));\n        } else {\n          return render(info);\n        }\n      };\n      return ListItemComponent ? {\n        ListItemComponent: renderProp\n      } : {\n        renderItem: renderProp\n      };\n    };\n    this._memoizedRenderer = memoizeOne(this._renderer);\n    this._checkProps(this.props);\n    if (this.props.viewabilityConfigCallbackPairs) {\n      this._virtualizedListPairs = this.props.viewabilityConfigCallbackPairs.map(pair => ({\n        viewabilityConfig: pair.viewabilityConfig,\n        onViewableItemsChanged: this._createOnViewableItemsChanged(pair.onViewableItemsChanged)\n      }));\n    } else if (this.props.onViewableItemsChanged) {\n      this._virtualizedListPairs.push({\n        /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.63 was deployed. To\n         * see the error delete this comment and run Flow. */\n        viewabilityConfig: this.props.viewabilityConfig,\n        onViewableItemsChanged: this._createOnViewableItemsChanged(this.props.onViewableItemsChanged)\n      });\n    }\n  }\n\n  // $FlowFixMe[missing-local-annot]\n  componentDidUpdate(prevProps) {\n    invariant(prevProps.numColumns === this.props.numColumns, 'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' + 'changing the number of columns to force a fresh render of the component.');\n    invariant(prevProps.onViewableItemsChanged === this.props.onViewableItemsChanged, 'Changing onViewableItemsChanged on the fly is not supported');\n    invariant(!deepDiffer(prevProps.viewabilityConfig, this.props.viewabilityConfig), 'Changing viewabilityConfig on the fly is not supported');\n    invariant(prevProps.viewabilityConfigCallbackPairs === this.props.viewabilityConfigCallbackPairs, 'Changing viewabilityConfigCallbackPairs on the fly is not supported');\n    this._checkProps(this.props);\n  }\n  // $FlowFixMe[missing-local-annot]\n  _checkProps(props) {\n    var getItem = props.getItem,\n      getItemCount = props.getItemCount,\n      horizontal = props.horizontal,\n      columnWrapperStyle = props.columnWrapperStyle,\n      onViewableItemsChanged = props.onViewableItemsChanged,\n      viewabilityConfigCallbackPairs = props.viewabilityConfigCallbackPairs;\n    var numColumns = numColumnsOrDefault(this.props.numColumns);\n    invariant(!getItem && !getItemCount, 'FlatList does not support custom data formats.');\n    if (numColumns > 1) {\n      invariant(!horizontal, 'numColumns does not support horizontal.');\n    } else {\n      invariant(!columnWrapperStyle, 'columnWrapperStyle not supported for single column lists');\n    }\n    invariant(!(onViewableItemsChanged && viewabilityConfigCallbackPairs), 'FlatList does not support setting both onViewableItemsChanged and ' + 'viewabilityConfigCallbackPairs.');\n  }\n  _pushMultiColumnViewable(arr, v) {\n    var _this$props$keyExtrac2;\n    var numColumns = numColumnsOrDefault(this.props.numColumns);\n    var keyExtractor = (_this$props$keyExtrac2 = this.props.keyExtractor) !== null && _this$props$keyExtrac2 !== void 0 ? _this$props$keyExtrac2 : defaultKeyExtractor;\n    v.item.forEach((item, ii) => {\n      invariant(v.index != null, 'Missing index!');\n      var index = v.index * numColumns + ii;\n      arr.push(_objectSpread(_objectSpread({}, v), {}, {\n        item,\n        key: keyExtractor(item, index),\n        index\n      }));\n    });\n  }\n  _createOnViewableItemsChanged(onViewableItemsChanged\n  // $FlowFixMe[missing-local-annot]\n  ) {\n    return info => {\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      if (onViewableItemsChanged) {\n        if (numColumns > 1) {\n          var changed = [];\n          var viewableItems = [];\n          info.viewableItems.forEach(v => this._pushMultiColumnViewable(viewableItems, v));\n          info.changed.forEach(v => this._pushMultiColumnViewable(changed, v));\n          onViewableItemsChanged({\n            viewableItems,\n            changed\n          });\n        } else {\n          onViewableItemsChanged(info);\n        }\n      }\n    };\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  render() {\n    var _this$props = this.props,\n      numColumns = _this$props.numColumns,\n      columnWrapperStyle = _this$props.columnWrapperStyle,\n      _removeClippedSubviews = _this$props.removeClippedSubviews,\n      _this$props$strictMod = _this$props.strictMode,\n      strictMode = _this$props$strictMod === void 0 ? false : _this$props$strictMod,\n      restProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n    var renderer = strictMode ? this._memoizedRenderer : this._renderer;\n    return (\n      /*#__PURE__*/\n      // $FlowFixMe[incompatible-exact] - `restProps` (`Props`) is inexact.\n      React.createElement(VirtualizedList, _extends({}, restProps, {\n        getItem: this._getItem,\n        getItemCount: this._getItemCount,\n        keyExtractor: this._keyExtractor,\n        ref: this._captureRef,\n        viewabilityConfigCallbackPairs: this._virtualizedListPairs,\n        removeClippedSubviews: removeClippedSubviewsOrDefault(_removeClippedSubviews)\n      }, renderer(this.props.ListItemComponent, this.props.renderItem, columnWrapperStyle, numColumns, this.props.extraData)))\n    );\n  }\n}\nvar styles = StyleSheet.create({\n  row: {\n    flexDirection: 'row'\n  }\n});\nexport default FlatList;"], "mappings": ";;;;;;;AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,OAAOC,aAAa,MAAM,sCAAsC;AAChE,IAAIC,SAAS,GAAG,CAAC,YAAY,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,YAAY,CAAC;AAW3F,OAAOC,IAAI;AACX,OAAOC,UAAU;AACjB,OAAOC,UAAU;AACjB,OAAOC,QAAQ;AACf,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe;AACtB,SAASC,YAAY,IAAIC,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,aAAa;AAOpC,SAASC,8BAA8BA,CAACC,qBAAqB,EAAE;EAC7D,OAAOA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGR,QAAQ,CAACS,EAAE,KAAK,SAAS;AAC/H;AAGA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC;AACtE;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EAEzB,OAAO,OAAOC,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,KAAK,QAAQ;AAChD;AAAC,IA6GKC,QAAQ,aAAAC,oBAAA;EA6FZ,SAAAD,SAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,QAAA;IAClBG,KAAA,GAAAE,UAAA,OAAAL,QAAA,GAAME,MAAM;IACZC,KAAA,CAAKG,qBAAqB,GAAG,EAAE;IAC/BH,KAAA,CAAKI,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxBL,KAAA,CAAKM,QAAQ,GAAGD,GAAG;IACrB,CAAC;IACDL,KAAA,CAAKO,QAAQ,GAAG,UAACb,IAAI,EAAEc,KAAK,EAAK;MAC/B,IAAIhB,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKS,KAAK,CAACjB,UAAU,CAAC;MAC3D,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClB,IAAIkB,GAAG,GAAG,EAAE;QACZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGnB,UAAU,EAAEmB,EAAE,EAAE,EAAE;UACtC,IAAIC,SAAS,GAAGJ,KAAK,GAAGhB,UAAU,GAAGmB,EAAE;UACvC,IAAIC,SAAS,GAAGlB,IAAI,CAACE,MAAM,EAAE;YAC3B,IAAIiB,KAAK,GAAGnB,IAAI,CAACkB,SAAS,CAAC;YAC3BF,GAAG,CAACI,IAAI,CAACD,KAAK,CAAC;UACjB;QACF;QACA,OAAOH,GAAG;MACZ,CAAC,MAAM;QACL,OAAOhB,IAAI,CAACc,KAAK,CAAC;MACpB;IACF,CAAC;IACDR,KAAA,CAAKe,aAAa,GAAG,UAAArB,IAAI,EAAI;MAO3B,IAAIA,IAAI,IAAI,IAAI,IAAID,WAAW,CAACC,IAAI,CAAC,EAAE;QACrC,IAAIF,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKS,KAAK,CAACjB,UAAU,CAAC;QAC3D,OAAOA,UAAU,GAAG,CAAC,GAAGwB,IAAI,CAACC,IAAI,CAACvB,IAAI,CAACE,MAAM,GAAGJ,UAAU,CAAC,GAAGE,IAAI,CAACE,MAAM;MAC3E,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC;IACDI,KAAA,CAAKkB,aAAa,GAAG,UAACC,KAAK,EAAEX,KAAK,EAAK;MACrC,IAAIY,qBAAqB;MACzB,IAAI5B,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKS,KAAK,CAACjB,UAAU,CAAC;MAC3D,IAAIP,YAAY,GAAG,CAACmC,qBAAqB,GAAGpB,KAAA,CAAKS,KAAK,CAACxB,YAAY,MAAM,IAAI,IAAImC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGlC,mBAAmB;MAC/J,IAAIM,UAAU,GAAG,CAAC,EAAE;QAClBV,SAAS,CAACuC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE,wFAAwF,GAAG,2DAA2D,EAAE3B,UAAU,CAAC;QACnM,OAAO2B,KAAK,CAACI,GAAG,CAAC,UAACC,IAAI,EAAEb,EAAE;UAAA,OAAK1B,YAAY,CAACuC,IAAI,EAAEhB,KAAK,GAAGhB,UAAU,GAAGmB,EAAE,CAAC;QAAA,EAAC,CAACc,IAAI,CAAC,GAAG,CAAC;MACvF;MAGA,OAAOxC,YAAY,CAACkC,KAAK,EAAEX,KAAK,CAAC;IACnC,CAAC;IACDR,KAAA,CAAK0B,SAAS,GAAG,UAACC,iBAAiB,EAAEC,UAAU,EAAEC,kBAAkB,EAAErC,UAAU,EAAEsC,SAAS,EAErF;MACH,IAAIC,IAAI,GAAGxC,mBAAmB,CAACC,UAAU,CAAC;MAC1C,IAAIwC,MAAM,GAAG,SAATA,MAAMA,CAAGvB,KAAK,EAAI;QACpB,IAAIkB,iBAAiB,EAAE;UAIrB,OAAoB5C,KAAK,CAACkD,aAAa,CAACN,iBAAiB,EAAElB,KAAK,CAAC;QACnE,CAAC,MAAM,IAAImB,UAAU,EAAE;UAErB,OAAOA,UAAU,CAACnB,KAAK,CAAC;QAC1B,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MACD,IAAIyB,UAAU,GAAG,SAAbA,UAAUA,CAAGC,IAAI,EAAI;QACvB,IAAIJ,IAAI,GAAG,CAAC,EAAE;UACZ,IAAIK,MAAM,GAAGD,IAAI,CAACX,IAAI;YACpBa,MAAM,GAAGF,IAAI,CAAC3B,KAAK;UACrB1B,SAAS,CAACuC,KAAK,CAACC,OAAO,CAACc,MAAM,CAAC,EAAE,6CAA6C,CAAC;UAC/E,OAAoBrD,KAAK,CAACkD,aAAa,CAACvD,IAAI,EAAE;YAC5C4D,KAAK,EAAE,CAACC,MAAM,CAACC,GAAG,EAAEX,kBAAkB;UACxC,CAAC,EAAEO,MAAM,CAACb,GAAG,CAAC,UAACkB,EAAE,EAAE9B,EAAE,EAAK;YACxB,IAAI+B,OAAO,GAAGV,MAAM,CAAC;cAEnBR,IAAI,EAAEiB,EAAE;cACRjC,KAAK,EAAE6B,MAAM,GAAGN,IAAI,GAAGpB,EAAE;cACzBgC,UAAU,EAAER,IAAI,CAACQ;YACnB,CAAC,CAAC;YACF,OAAOD,OAAO,IAAI,IAAI,GAAgB3D,KAAK,CAACkD,aAAa,CAAClD,KAAK,CAAC6D,QAAQ,EAAE;cACxEC,GAAG,EAAElC;YACP,CAAC,EAAE+B,OAAO,CAAC,GAAG,IAAI;UACpB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,OAAOV,MAAM,CAACG,IAAI,CAAC;QACrB;MACF,CAAC;MACD,OAAOR,iBAAiB,GAAG;QACzBA,iBAAiB,EAAEO;MACrB,CAAC,GAAG;QACFN,UAAU,EAAEM;MACd,CAAC;IACH,CAAC;IACDlC,KAAA,CAAK8C,iBAAiB,GAAG3D,UAAU,CAACa,KAAA,CAAK0B,SAAS,CAAC;IACnD1B,KAAA,CAAK+C,WAAW,CAAC/C,KAAA,CAAKS,KAAK,CAAC;IAC5B,IAAIT,KAAA,CAAKS,KAAK,CAACuC,8BAA8B,EAAE;MAC7ChD,KAAA,CAAKG,qBAAqB,GAAGH,KAAA,CAAKS,KAAK,CAACuC,8BAA8B,CAACzB,GAAG,CAAC,UAAA0B,IAAI;QAAA,OAAK;UAClFC,iBAAiB,EAAED,IAAI,CAACC,iBAAiB;UACzCC,sBAAsB,EAAEnD,KAAA,CAAKoD,6BAA6B,CAACH,IAAI,CAACE,sBAAsB;QACxF,CAAC;MAAA,CAAC,CAAC;IACL,CAAC,MAAM,IAAInD,KAAA,CAAKS,KAAK,CAAC0C,sBAAsB,EAAE;MAC5CnD,KAAA,CAAKG,qBAAqB,CAACW,IAAI,CAAC;QAI9BoC,iBAAiB,EAAElD,KAAA,CAAKS,KAAK,CAACyC,iBAAiB;QAC/CC,sBAAsB,EAAEnD,KAAA,CAAKoD,6BAA6B,CAACpD,KAAA,CAAKS,KAAK,CAAC0C,sBAAsB;MAC9F,CAAC,CAAC;IACJ;IAAC,OAAAnD,KAAA;EACH;EAACqD,SAAA,CAAAxD,QAAA,EAAAC,oBAAA;EAAA,OAAAwD,YAAA,CAAAzD,QAAA;IAAAgD,GAAA;IAAAU,KAAA,EAtMD,SAAAC,WAAWA,CAACC,MAAM,EAAE;MAClB,IAAI,IAAI,CAACnD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACkD,WAAW,CAACC,MAAM,CAAC;MACnC;IACF;EAAC;IAAAZ,GAAA;IAAAU,KAAA,EAUD,SAAAG,aAAaA,CAACD,MAAM,EAAE;MACpB,IAAI,IAAI,CAACnD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACoD,aAAa,CAACD,MAAM,CAAC;MACrC;IACF;EAAC;IAAAZ,GAAA;IAAAU,KAAA,EAQD,SAAAI,YAAYA,CAACF,MAAM,EAAE;MACnB,IAAI,IAAI,CAACnD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACqD,YAAY,CAACF,MAAM,CAAC;MACpC;IACF;EAAC;IAAAZ,GAAA;IAAAU,KAAA,EAOD,SAAAK,cAAcA,CAACH,MAAM,EAAE;MACrB,IAAI,IAAI,CAACnD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACsD,cAAc,CAACH,MAAM,CAAC;MACtC;IACF;EAAC;IAAAZ,GAAA;IAAAU,KAAA,EAOD,SAAAM,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACuD,iBAAiB,CAAC,CAAC;MACnC;IACF;EAAC;IAAAhB,GAAA;IAAAU,KAAA,EAOD,SAAAO,qBAAqBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACxD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACwD,qBAAqB,CAAC,CAAC;MACvC;IACF;EAAC;IAAAjB,GAAA;IAAAU,KAAA,EAKD,SAAAQ,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACzD,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAACyD,kBAAkB,CAAC,CAAC;MAC3C;IACF;EAAC;IAAAlB,GAAA;IAAAU,KAAA,EAKD,SAAAS,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC1D,QAAQ,EAAE;QAGjB,OAAO,IAAI,CAACA,QAAQ,CAAC2D,YAAY,CAAC,CAAC;MACrC;IACF;EAAC;IAAApB,GAAA;IAAAU,KAAA,EACD,SAAAW,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC5D,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAAC4D,iBAAiB,CAAC,CAAC;MAC1C;IACF;EAAC;IAAArB,GAAA;IAAAU,KAAA,EAiHD,SAAAY,kBAAkBA,CAACC,SAAS,EAAE;MAC5BtF,SAAS,CAACsF,SAAS,CAAC5E,UAAU,KAAK,IAAI,CAACiB,KAAK,CAACjB,UAAU,EAAE,wFAAwF,GAAG,0EAA0E,CAAC;MAChOV,SAAS,CAACsF,SAAS,CAACjB,sBAAsB,KAAK,IAAI,CAAC1C,KAAK,CAAC0C,sBAAsB,EAAE,6DAA6D,CAAC;MAChJrE,SAAS,CAAC,CAACF,UAAU,CAACwF,SAAS,CAAClB,iBAAiB,EAAE,IAAI,CAACzC,KAAK,CAACyC,iBAAiB,CAAC,EAAE,wDAAwD,CAAC;MAC3IpE,SAAS,CAACsF,SAAS,CAACpB,8BAA8B,KAAK,IAAI,CAACvC,KAAK,CAACuC,8BAA8B,EAAE,qEAAqE,CAAC;MACxK,IAAI,CAACD,WAAW,CAAC,IAAI,CAACtC,KAAK,CAAC;IAC9B;EAAC;IAAAoC,GAAA;IAAAU,KAAA,EAED,SAAAR,WAAWA,CAACtC,KAAK,EAAE;MACjB,IAAI4D,OAAO,GAAG5D,KAAK,CAAC4D,OAAO;QACzBC,YAAY,GAAG7D,KAAK,CAAC6D,YAAY;QACjCC,UAAU,GAAG9D,KAAK,CAAC8D,UAAU;QAC7B1C,kBAAkB,GAAGpB,KAAK,CAACoB,kBAAkB;QAC7CsB,sBAAsB,GAAG1C,KAAK,CAAC0C,sBAAsB;QACrDH,8BAA8B,GAAGvC,KAAK,CAACuC,8BAA8B;MACvE,IAAIxD,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACkB,KAAK,CAACjB,UAAU,CAAC;MAC3DV,SAAS,CAAC,CAACuF,OAAO,IAAI,CAACC,YAAY,EAAE,gDAAgD,CAAC;MACtF,IAAI9E,UAAU,GAAG,CAAC,EAAE;QAClBV,SAAS,CAAC,CAACyF,UAAU,EAAE,yCAAyC,CAAC;MACnE,CAAC,MAAM;QACLzF,SAAS,CAAC,CAAC+C,kBAAkB,EAAE,0DAA0D,CAAC;MAC5F;MACA/C,SAAS,CAAC,EAAEqE,sBAAsB,IAAIH,8BAA8B,CAAC,EAAE,oEAAoE,GAAG,iCAAiC,CAAC;IAClL;EAAC;IAAAH,GAAA;IAAAU,KAAA,EACD,SAAAiB,wBAAwBA,CAACC,GAAG,EAAEC,CAAC,EAAE;MAC/B,IAAIC,sBAAsB;MAC1B,IAAInF,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACkB,KAAK,CAACjB,UAAU,CAAC;MAC3D,IAAIP,YAAY,GAAG,CAAC0F,sBAAsB,GAAG,IAAI,CAAClE,KAAK,CAACxB,YAAY,MAAM,IAAI,IAAI0F,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAGzF,mBAAmB;MAClKwF,CAAC,CAAClD,IAAI,CAACoD,OAAO,CAAC,UAACpD,IAAI,EAAEqD,EAAE,EAAK;QAC3B/F,SAAS,CAAC4F,CAAC,CAAClE,KAAK,IAAI,IAAI,EAAE,gBAAgB,CAAC;QAC5C,IAAIA,KAAK,GAAGkE,CAAC,CAAClE,KAAK,GAAGhB,UAAU,GAAGqF,EAAE;QACrCJ,GAAG,CAAC3D,IAAI,CAACtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/ClD,IAAI,EAAJA,IAAI;UACJqB,GAAG,EAAE5D,YAAY,CAACuC,IAAI,EAAEhB,KAAK,CAAC;UAC9BA,KAAK,EAALA;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;EAAC;IAAAqC,GAAA;IAAAU,KAAA,EACD,SAAAH,6BAA6BA,CAACD,sBAAsB,EAElD;MAAA,IAAA2B,MAAA;MACA,OAAO,UAAA3C,IAAI,EAAI;QACb,IAAI3C,UAAU,GAAGD,mBAAmB,CAACuF,MAAI,CAACrE,KAAK,CAACjB,UAAU,CAAC;QAC3D,IAAI2D,sBAAsB,EAAE;UAC1B,IAAI3D,UAAU,GAAG,CAAC,EAAE;YAClB,IAAIuF,OAAO,GAAG,EAAE;YAChB,IAAIC,aAAa,GAAG,EAAE;YACtB7C,IAAI,CAAC6C,aAAa,CAACJ,OAAO,CAAC,UAAAF,CAAC;cAAA,OAAII,MAAI,CAACN,wBAAwB,CAACQ,aAAa,EAAEN,CAAC,CAAC;YAAA,EAAC;YAChFvC,IAAI,CAAC4C,OAAO,CAACH,OAAO,CAAC,UAAAF,CAAC;cAAA,OAAII,MAAI,CAACN,wBAAwB,CAACO,OAAO,EAAEL,CAAC,CAAC;YAAA,EAAC;YACpEvB,sBAAsB,CAAC;cACrB6B,aAAa,EAAbA,aAAa;cACbD,OAAO,EAAPA;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL5B,sBAAsB,CAAChB,IAAI,CAAC;UAC9B;QACF;MACF,CAAC;IACH;EAAC;IAAAU,GAAA;IAAAU,KAAA,EAID,SAAAvB,MAAMA,CAAA,EAAG;MACP,IAAIiD,WAAW,GAAG,IAAI,CAACxE,KAAK;QAC1BjB,UAAU,GAAGyF,WAAW,CAACzF,UAAU;QACnCqC,kBAAkB,GAAGoD,WAAW,CAACpD,kBAAkB;QACnDqD,sBAAsB,GAAGD,WAAW,CAAC5F,qBAAqB;QAC1D8F,qBAAqB,GAAGF,WAAW,CAACG,UAAU;QAC9CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;QAC7EE,SAAS,GAAG9G,6BAA6B,CAAC0G,WAAW,EAAExG,SAAS,CAAC;MACnE,IAAI6G,QAAQ,GAAGF,UAAU,GAAG,IAAI,CAACtC,iBAAiB,GAAG,IAAI,CAACpB,SAAS;MACnE,QAGE3C,KAAK,CAACkD,aAAa,CAACjD,eAAe,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAE+G,SAAS,EAAE;UAC3DhB,OAAO,EAAE,IAAI,CAAC9D,QAAQ;UACtB+D,YAAY,EAAE,IAAI,CAACvD,aAAa;UAChC9B,YAAY,EAAE,IAAI,CAACiC,aAAa;UAChCb,GAAG,EAAE,IAAI,CAACD,WAAW;UACrB4C,8BAA8B,EAAE,IAAI,CAAC7C,qBAAqB;UAC1Dd,qBAAqB,EAAED,8BAA8B,CAAC8F,sBAAsB;QAC9E,CAAC,EAAEI,QAAQ,CAAC,IAAI,CAAC7E,KAAK,CAACkB,iBAAiB,EAAE,IAAI,CAAClB,KAAK,CAACmB,UAAU,EAAEC,kBAAkB,EAAErC,UAAU,EAAE,IAAI,CAACiB,KAAK,CAACqB,SAAS,CAAC,CAAC;MAAC;IAE5H;EAAC;AAAA,EAhSoB/C,KAAK,CAACwG,aAAa;AAkS1C,IAAIhD,MAAM,GAAG5D,UAAU,CAAC6G,MAAM,CAAC;EAC7BhD,GAAG,EAAE;IACHiD,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACF,eAAe5F,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}