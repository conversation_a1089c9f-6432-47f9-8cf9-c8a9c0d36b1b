{"ast": null, "code": "\"use client\";\n\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { useState, useEffect } from \"react\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport SafeAreaView from \"react-native-web/dist/exports/SafeAreaView\";\nimport StatusBar from \"react-native-web/dist/exports/StatusBar\";\nimport SplashScreen from \"./src/screens/SplashScreen\";\nimport ServiceProvidersScreen from \"./src/screens/ServiceProvidersScreen\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar App = function App() {\n  var _useState = useState(true),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLoading = _useState2[0],\n    setIsLoading = _useState2[1];\n  useEffect(function () {\n    var timer = setTimeout(function () {\n      setIsLoading(false);\n    }, 3000);\n    return function () {\n      return clearTimeout(timer);\n    };\n  }, []);\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsx(StatusBar, {\n      barStyle: \"light-content\",\n      backgroundColor: \"#4A90E2\"\n    }), isLoading ? _jsx(SplashScreen, {}) : _jsx(ServiceProvidersScreen, {})]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: \"#f5f5f5\"\n  }\n});\nexport default App;", "map": {"version": 3, "names": ["_slicedToArray", "useState", "useEffect", "StyleSheet", "SafeAreaView", "StatusBar", "SplashScreen", "ServiceProvidersScreen", "jsx", "_jsx", "jsxs", "_jsxs", "App", "_useState", "_useState2", "isLoading", "setIsLoading", "timer", "setTimeout", "clearTimeout", "style", "styles", "container", "children", "barStyle", "backgroundColor", "create", "flex"], "sources": ["/Users/<USER>/Downloads/logistics-app/App.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { StyleSheet, SafeAreaView, StatusBar } from \"react-native\"\nimport SplashScreen from \"./src/screens/SplashScreen\"\nimport ServiceProvidersScreen from \"./src/screens/ServiceProvidersScreen\"\n\nconst App = () => {\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Simulate splash screen loading time\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 3000)\n\n    return () => clearTimeout(timer)\n  }, [])\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <StatusBar barStyle=\"light-content\" backgroundColor=\"#4A90E2\" />\n      {isLoading ? <SplashScreen /> : <ServiceProvidersScreen />}\n    </SafeAreaView>\n  )\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: \"#f5f5f5\",\n  },\n})\n\nexport default App\n"], "mappings": "AAAA,YAAY;;AAAA,OAAAA,cAAA;AAEZ,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,SAAA;AAE3C,OAAOC,YAAY;AACnB,OAAOC,sBAAsB;AAA4C,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzE,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAA,EAAS;EAChB,IAAAC,SAAA,GAAkCZ,QAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAd,cAAA,CAAAa,SAAA;IAAzCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAE9BZ,SAAS,CAAC,YAAM;IAEd,IAAMe,KAAK,GAAGC,UAAU,CAAC,YAAM;MAC7BF,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO;MAAA,OAAMG,YAAY,CAACF,KAAK,CAAC;IAAA;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,OACEN,KAAA,CAACP,YAAY;IAACgB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GACpCd,IAAA,CAACJ,SAAS;MAACmB,QAAQ,EAAC,eAAe;MAACC,eAAe,EAAC;IAAS,CAAE,CAAC,EAC/DV,SAAS,GAAGN,IAAA,CAACH,YAAY,IAAE,CAAC,GAAGG,IAAA,CAACF,sBAAsB,IAAE,CAAC;EAAA,CAC9C,CAAC;AAEnB,CAAC;AAED,IAAMc,MAAM,GAAGlB,UAAU,CAACuB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,IAAI,EAAE,CAAC;IACPF,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAeb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}