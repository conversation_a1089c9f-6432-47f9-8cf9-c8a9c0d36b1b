{"ast": null, "code": "import { UnavailabilityError } from \"./errors/UnavailabilityError\";\nexport function requireNativeViewManager(viewName) {\n  throw new UnavailabilityError('expo-modules-core', 'requireNativeViewManager');\n}", "map": {"version": 3, "names": ["UnavailabilityError", "requireNativeViewManager", "viewName"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/expo-modules-core/src/NativeViewManagerAdapter.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { UnavailabilityError } from './errors/UnavailabilityError';\n\nexport function requireNativeViewManager<P = any>(viewName: string): React.ComponentType<P> {\n  throw new UnavailabilityError('expo-modules-core', 'requireNativeViewManager');\n}\n"], "mappings": "AAEA,SAASA,mBAAmB;AAE5B,OAAM,SAAUC,wBAAwBA,CAAUC,QAAgB;EAChE,MAAM,IAAIF,mBAAmB,CAAC,mBAAmB,EAAE,0BAA0B,CAAC;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}