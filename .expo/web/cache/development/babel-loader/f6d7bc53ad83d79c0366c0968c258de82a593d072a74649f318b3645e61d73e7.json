{"ast": null, "code": "import * as React from 'react';\nimport Image from \"../../../../exports/Image\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nexport default createAnimatedComponent(Image);", "map": {"version": 3, "names": ["React", "Image", "createAnimatedComponent"], "sources": ["/Users/<USER>/Downloads/logistics-app/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedImage.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport Image from '../../../../exports/Image';\nimport createAnimatedComponent from '../createAnimatedComponent';\nexport default createAnimatedComponent(Image);"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK;AACZ,OAAOC,uBAAuB;AAC9B,eAAeA,uBAAuB,CAACD,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}