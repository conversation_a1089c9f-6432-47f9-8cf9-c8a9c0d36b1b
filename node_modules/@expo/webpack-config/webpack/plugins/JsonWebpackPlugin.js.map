{"version": 3, "file": "JsonWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/JsonWebpackPlugin.ts"], "names": [], "mappings": ";;AAAA,qCAAmD;AACnD,qCAAyD;AAezD,MAAM,QAAQ,GAAG,IAAI,OAAO,EAA4D,CAAC;AAEzF,SAAS,wBAAwB;IAC/B,OAAO;QACL,UAAU,EAAE,IAAI,kCAAwB,CAAoB,CAAC,YAAY,CAAC,CAAC;QAC3E,SAAS,EAAE,IAAI,kCAAwB,CAAmB,CAAC,YAAY,CAAC,CAAC;KAC1E,CAAC;AACJ,CAAC;AAED,MAAqB,iBAAiB;IAWpC,YAAmB,OAAgB;QAAhB,YAAO,GAAP,OAAO,CAAS;QAqB3B,gBAAW,GAAG,KAAK,EAAE,WAAwB,EAAiB,EAAE;YACtE,IAAI,MAAM,GAAsB;gBAC9B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;gBACvB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;gBACvB,MAAM,EAAE,IAAI;aACb,CAAC;YACF,IAAI;gBACF,MAAM,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACnF;YAAC,OAAO,KAAU,EAAE;gBACnB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAEzF,sDAAsD;YACtD,oCAAoC;YACpC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YAEhE,MAAM,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC9D,IAAI;gBACJ,UAAU,EAAE,MAAM,CAAC,IAAI;gBACvB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC,CAAC;QA3CA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;SAC3F;IACH,CAAC;IAdD,MAAM,CAAC,QAAQ,CAAC,WAAwB;QACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACtC,4BAA4B;QAC5B,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,KAAK,GAAG,wBAAwB,EAAE,CAAC;YACnC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAQD,KAAK,CAAC,QAAkB;QACtB,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,WAAwB,EAAE,EAAE;YACjF,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CACxC;gBACE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC3B,0EAA0E;gBAC1E,KAAK,EAAE,qBAAW,CAAC,+BAA+B;aACnD,EACD,KAAK,IAAI,EAAE;gBACT,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACtC,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CA0BF;AAxDD,oCAwDC", "sourcesContent": ["import { AsyncSeriesWaterfallHook } from 'tapable';\nimport { Compilation, Compiler, sources } from 'webpack';\n\nexport type Options = {\n  path: string;\n  json: any;\n  pretty?: boolean;\n};\n\nexport type BeforeEmitOptions = Options & { plugin: JsonWebpackPlugin };\n\nexport type AfterEmitOptions = Pick<Options, 'json'> & {\n  outputName: string;\n  plugin: JsonWebpackPlugin;\n};\n\nconst hooksMap = new WeakMap<Compilation, ReturnType<typeof createWebpackPluginHooks>>();\n\nfunction createWebpackPluginHooks() {\n  return {\n    beforeEmit: new AsyncSeriesWaterfallHook<BeforeEmitOptions>(['pluginArgs']),\n    afterEmit: new AsyncSeriesWaterfallHook<AfterEmitOptions>(['pluginArgs']),\n  };\n}\n\nexport default class JsonWebpackPlugin {\n  static getHooks(compilation: Compilation) {\n    let hooks = hooksMap.get(compilation);\n    // Setup the hooks only once\n    if (hooks === undefined) {\n      hooks = createWebpackPluginHooks();\n      hooksMap.set(compilation, hooks);\n    }\n    return hooks;\n  }\n\n  constructor(public options: Options) {\n    if (!this.options.path || !this.options.json) {\n      throw new Error('Failed to write json because either `path` or `json` were not defined.');\n    }\n  }\n\n  apply(compiler: Compiler) {\n    compiler.hooks.compilation.tap(this.constructor.name, (compilation: Compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: this.constructor.name,\n          // https://github.com/webpack/webpack/blob/master/lib/Compilation.js#L3280\n          stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,\n        },\n        async () => {\n          await this.writeObject(compilation);\n        }\n      );\n    });\n  }\n\n  private writeObject = async (compilation: Compilation): Promise<void> => {\n    let result: BeforeEmitOptions = {\n      json: this.options.json,\n      path: this.options.path,\n      plugin: this,\n    };\n    try {\n      result = await JsonWebpackPlugin.getHooks(compilation).beforeEmit.promise(result);\n    } catch (error: any) {\n      compilation.errors.push(error);\n    }\n\n    const json = JSON.stringify(result.json, undefined, this.options.pretty ? 2 : undefined);\n\n    // Once all files are added to the webpack compilation\n    // let the webpack compiler continue\n    compilation.emitAsset(result.path, new sources.RawSource(json));\n\n    await JsonWebpackPlugin.getHooks(compilation).afterEmit.promise({\n      json,\n      outputName: result.path,\n      plugin: this,\n    });\n  };\n}\n"]}