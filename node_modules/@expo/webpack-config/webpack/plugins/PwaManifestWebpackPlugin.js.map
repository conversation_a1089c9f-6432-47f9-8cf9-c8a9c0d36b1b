{"version": 3, "file": "PwaManifestWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/PwaManifestWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,uCAAuC;AACvC,qCAAqF;AAErF,4EAAoD;AASpD,SAAS,gBAAgB,CAAC,QAAkB,EAAE,IAAY;;IACxD,OAAO,MAAA,MAAA,QAAQ,CAAC,OAAO,0CAAE,OAAO,0CAC5B,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,WAAW,EACrC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACnE,CAAC;AAQD,MAAqB,wBAAyB,SAAQ,2BAAiB;IAGrE,YAAoB,UAA8B,EAAE,QAAa;QAC/D,KAAK,CAAC;YACJ,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QALe,eAAU,GAAV,UAAU,CAAoB;QAFlD,QAAG,GAAW,UAAU,CAAC;IAQzB,CAAC;IAED,KAAK,CAAC,QAAkB;QACtB,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEtB,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,WAAwB,EAAE,EAAE;YACjF,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CACxC;gBACE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC3B,0EAA0E;gBAC1E,KAAK,EAAE,qBAAW,CAAC,+BAA+B;aACnD,EACD,KAAK,IAAI,EAAE;gBACT,gEAAgE;gBAChE,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,mBAAmB,CAAQ,CAAC;gBACjF,IAAI,iBAAiB,EAAE;oBACrB,IAAI,OAAO,iBAAiB,CAAC,QAAQ,KAAK,WAAW,EAAE;wBACrD,WAAW,CAAC,MAAM,CAAC,IAAI,CACrB,IAAI,sBAAY,CACd,mIAAmI,CACpI,CACF,CAAC;wBACF,OAAO;qBACR;oBAED,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,QAAQ,CAC7D,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,CACE,IAAoB,EACpB,YAAiE,EACjE,EAAE;wBACF,sDAAsD;wBACtD,kEAAkE;wBAClE,IAAI,kBAA2B,CAAC;wBAChC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;4BAC/C,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;yBAC7C;6BAAM,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,UAAU,EAAE;4BACvD,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBAC1D;6BAAM;4BACL,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;yBAChE;wBAED,IAAI,kBAAkB,KAAK,KAAK,EAAE;4BAChC,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;yBACjC;wBAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,MAAM;4BACf,OAAO,EAAE,IAAI;4BACb,UAAU,EAAE;gCACV,GAAG,EAAE,IAAI,CAAC,GAAG;gCACb,IAAI,EAAE,IAAA,sBAAW,EAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;6BACpE;yBACF,CAAC,CAAC;wBAEH,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3B,CAAC,CACF,CAAC;iBACH;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxED,2CAwEC", "sourcesContent": ["import { joinUrlPath } from 'expo-pwa';\nimport { Compilation, Compiler, WebpackError, WebpackPluginInstance } from 'webpack';\n\nimport JsonWebpackPlugin from './JsonWebpackPlugin';\nimport { HTMLPluginData } from './ModifyHtmlWebpackPlugin';\n\nexport type Icon = {\n  src: string;\n  sizes: string;\n  type: 'image/png';\n};\n\nfunction maybeFetchPlugin(compiler: Compiler, name: string): WebpackPluginInstance | undefined {\n  return compiler.options?.plugins\n    ?.map(({ constructor }) => constructor)\n    .find(constructor => constructor && constructor.name === name);\n}\n\nexport type PwaManifestOptions = {\n  path: string;\n  inject?: boolean | Function;\n  publicPath: string;\n};\n\nexport default class PwaManifestWebpackPlugin extends JsonWebpackPlugin {\n  rel: string = 'manifest';\n\n  constructor(private pwaOptions: PwaManifestOptions, manifest: any) {\n    super({\n      path: pwaOptions.path,\n      json: manifest,\n      pretty: true,\n    });\n  }\n\n  apply(compiler: Compiler) {\n    super.apply(compiler);\n\n    compiler.hooks.compilation.tap(this.constructor.name, (compilation: Compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: this.constructor.name,\n          // https://github.com/webpack/webpack/blob/master/lib/Compilation.js#L3280\n          stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,\n        },\n        async () => {\n          // Hook into the html-webpack-plugin processing and add the html\n          const HtmlWebpackPlugin = maybeFetchPlugin(compiler, 'HtmlWebpackPlugin') as any;\n          if (HtmlWebpackPlugin) {\n            if (typeof HtmlWebpackPlugin.getHooks === 'undefined') {\n              compilation.errors.push(\n                new WebpackError(\n                  'PwaManifestWebpackPlugin - This PwaManifestWebpackPlugin version is not compatible with your current HtmlWebpackPlugin version.\\n'\n                )\n              );\n              return;\n            }\n\n            HtmlWebpackPlugin.getHooks(compilation).alterAssetTags.tapAsync(\n              this.constructor.name,\n              (\n                data: HTMLPluginData,\n                htmlCallback: (error: Error | null, data: HTMLPluginData) => void\n              ) => {\n                // Skip if a custom injectFunction returns false or if\n                // the htmlWebpackPlugin options includes a `favicons: false` flag\n                let isInjectionAllowed: boolean;\n                if (typeof this.pwaOptions.inject === 'boolean') {\n                  isInjectionAllowed = this.pwaOptions.inject;\n                } else if (typeof this.pwaOptions.inject === 'function') {\n                  isInjectionAllowed = this.pwaOptions.inject(data.plugin);\n                } else {\n                  isInjectionAllowed = data.plugin.options.pwaManifest !== false;\n                }\n\n                if (isInjectionAllowed === false) {\n                  return htmlCallback(null, data);\n                }\n\n                data.assetTags.meta.push({\n                  tagName: 'link',\n                  voidTag: true,\n                  attributes: {\n                    rel: this.rel,\n                    href: joinUrlPath(this.pwaOptions.publicPath, this.pwaOptions.path),\n                  },\n                });\n\n                htmlCallback(null, data);\n              }\n            );\n          }\n        }\n      );\n    });\n  }\n}\n"]}