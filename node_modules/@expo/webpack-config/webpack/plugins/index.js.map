{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/plugins/index.ts"], "names": [], "mappings": ";;;;;;AAAA,uDAAiE;AAAxD,qIAAA,OAAO,OAAoB;AACpC,iEAA2E;AAAlE,+IAAA,OAAO,OAAyB;AACzC,yEAAmF;AAA1E,uJAAA,OAAO,OAA6B;AAC7C,iEAA2E;AAAlE,+IAAA,OAAO,OAAyB;AACzC,yDAAmE;AAA1D,uIAAA,OAAO,OAAqB;AACrC,qEAA+E;AAAtE,mJAAA,OAAO,OAA2B;AAC3C,uEAAiF;AAAxE,qJAAA,OAAO,OAA4B;AAC5C,iEAA2E;AAAlE,+IAAA,OAAO,OAAyB;AACzC,uEAAiF;AAAxE,qJAAA,OAAO,OAA4B;AAC5C,+EAAyF;AAAhF,6JAAA,OAAO,OAAgC;AAChD,qEAA+E;AAAtE,mJAAA,OAAO,OAA2B;AAC3C,+DAAyE;AAAhE,6IAAA,OAAO,OAAwB;AACxC,8EAAwF;AAA/E,6IAAA,OAAO,OAAwB", "sourcesContent": ["export { default as ExpoDefinePlugin } from './ExpoDefinePlugin';\nexport { default as ExpoHtmlWebpackPlugin } from './ExpoHtmlWebpackPlugin';\nexport { default as ExpoInterpolateHtmlPlugin } from './ExpoInterpolateHtmlPlugin';\nexport { default as ExpoProgressBarPlugin } from './ExpoProgressBarPlugin';\nexport { default as JsonWebpackPlugin } from './JsonWebpackPlugin';\nexport { default as ModifyJsonWebpackPlugin } from './ModifyJsonWebpackPlugin';\nexport { default as PwaManifestWebpackPlugin } from './PwaManifestWebpackPlugin';\nexport { default as ApplePwaWebpackPlugin } from './ApplePwaWebpackPlugin';\nexport { default as ChromeIconsWebpackPlugin } from './ChromeIconsWebpackPlugin';\nexport { default as ExpoPwaManifestWebpackPlugin } from './ExpoPwaManifestWebpackPlugin';\nexport { default as ModifyHtmlWebpackPlugin } from './ModifyHtmlWebpackPlugin';\nexport { default as FaviconWebpackPlugin } from './FaviconWebpackPlugin';\nexport { default as ExpectedErrorsPlugin } from './ExpectedErrors/ExpectedErrorsPlugin';\n"]}