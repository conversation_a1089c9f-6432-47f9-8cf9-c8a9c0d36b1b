{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type Log from '@expo/bunyan';\nimport type { PWAConfig } from 'expo-pwa';\nimport type { ProxyConfigArray, ProxyConfigMap } from 'webpack-dev-server';\n\ntype AnyObject = Record<string, any>;\n\nexport type InputEnvironment = {\n  projectRoot?: string;\n  platform?: 'ios' | 'android' | 'web' | 'electron';\n  info?: boolean;\n  https?: boolean;\n  production?: boolean;\n  development?: boolean;\n  config?: AnyObject;\n  locations?: FilePaths;\n  mode?: Mode;\n  pwa?: boolean;\n  babel?: {\n    dangerouslyAddModulePathsToTranspile: string[];\n  };\n  logger?: Log;\n  port?: number;\n};\n\nexport type Environment = {\n  /**\n   * Should the dev server use https protocol.\n   *\n   * @default false\n   */\n  https: boolean;\n  /**\n   * The Expo project config, this should be read using `@expo/config`.\n   *\n   * @default undefined\n   */\n  config: PWAConfig;\n  /**\n   * Paths used to locate where things are.\n   */\n  locations: FilePaths;\n  /**\n   * Root of the Expo project.\n   */\n  projectRoot: string;\n  /**\n   * The Webpack mode to bundle the project in.\n   */\n  mode: Mode;\n  /**\n   * The target platform to bundle for. Currently only `web` and `electron` are supported.\n   */\n  platform: ExpoPlatform;\n  /**\n   * Generate the PWA image assets in production mode.\n   *\n   * @default true\n   */\n  pwa?: boolean;\n  /**\n   * Control how the default Babel loader is configured.\n   */\n  babel?: ExpoBabelOptions;\n  /**\n   * Used for sending unified bundler logs to Expo CLI.\n   */\n  logger?: Log;\n\n  /**\n   * Dev server port.\n   */\n  port?: number;\n};\n\n/**\n * The target platform to bundle for. Currently only `web` and `electron` are supported.\n */\nexport type ExpoPlatform = 'ios' | 'android' | 'web' | 'electron';\n\n/**\n * Control how the default Babel loader is configured.\n */\nexport type ExpoBabelOptions = {\n  /**\n   * Add the names of node_modules that should be included transpilation step.\n   */\n  dangerouslyAddModulePathsToTranspile: string[];\n};\n\ntype PathResolver = (...input: string[]) => string;\n\nexport interface FilePathsFolder {\n  get: PathResolver;\n  folder: string;\n  indexHtml: string;\n  manifest: string;\n  serveJson: string;\n  favicon: string;\n}\nexport interface FilePaths {\n  absolute: PathResolver;\n  includeModule: PathResolver;\n  template: FilePathsFolder;\n  production: FilePathsFolder;\n  packageJson: string;\n  root: string;\n  appMain: string | null;\n  modules: string;\n  servedPath: string;\n  appWebpackCache: string;\n  appTsConfig: string;\n  appJsConfig: string;\n  //   [route: string]: string | PathResolver | FilePathsFolder;\n}\n\nexport type Mode = 'production' | 'development' | 'none';\n\nexport interface Arguments {\n  allowedHost?: string;\n  proxy?: ProxyConfigMap | ProxyConfigArray;\n  [key: string]: any;\n}\n"]}