{"version": 3, "file": "array.js", "sourceRoot": "", "sources": ["../../src/utils/array.ts"], "names": [], "mappings": ";;;AAAA,kFAAkF;AAClF,SAAgB,MAAM,CAAI,KAAmC;IAC3D,OAAO,CAAC,CAAC,KAAK,CAAC;AACjB,CAAC;AAFD,wBAEC", "sourcesContent": ["/** A predicate to filter arrays on truthy values, returning a type-safe array. */\nexport function truthy<T>(value: T | null | undefined | false): value is T {\n  return !!value;\n}\n"]}