{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/utils/config.ts"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,CAAC,GAAQ;IACxB,IAAI,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,OAAO,GAAG,KAAK,UAAU,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AAC9D,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,0BAA0B,CACxC,IAAS,EACT,MAAwC,EACxC,QAAiB,KAAK;IAEtB,mBAAmB;IACnB,IAAI,IAAI,EAAE;QACR,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClB,IAAI,KAAK,EAAE;gBACT,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAChD,MAAM,IAAI,KAAK,CAAC,kDAAkD,GAAG,MAAM,CAAC,CAAC;iBAC9E;gBACD,OAAO;oBACL,GAAG,MAAM;oBACT,GAAG,IAAI;iBACR,CAAC;aACH;YAED,kBAAkB;YAClB,OAAO,IAAI,CAAC;SACb;QAED,gFAAgF;QAChF,OAAO,MAAM,CAAC;KACf;IACD,iBAAiB;IACjB,OAAO,IAAI,CAAC;AACd,CAAC;AA3BD,gEA2BC;AAED;;;;GAIG;AACH,SAAgB,4BAA4B,CAC1C,IAAS,EACT,MAAwC,EACxC,QAAiB,KAAK;IAEtB,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,MAAM,CAAC;KACf;IACD,OAAO,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzD,CAAC;AATD,oEASC", "sourcesContent": ["function isObject(val: any): boolean {\n  if (val === null) {\n    return false;\n  }\n  return typeof val === 'function' || typeof val === 'object';\n}\n\n/**\n * Given a config option that could evalutate to true, config, or null; return a config.\n * e.g.\n * `polyfill: true` returns the `config`\n * `polyfill: {}` returns `{}`\n *\n * @category utils\n */\nexport function enableWithPropertyOrConfig(\n  prop: any,\n  config: boolean | { [key: string]: any },\n  merge: boolean = false\n): any {\n  // Value is truthy.\n  if (prop) {\n    if (isObject(prop)) {\n      if (merge) {\n        if (config == null || typeof config !== 'object') {\n          throw new Error('enableWithPropertyOrConfig cannot merge config: ' + config);\n        }\n        return {\n          ...config,\n          ...prop,\n        };\n      }\n\n      // Return property\n      return prop;\n    }\n\n    // Value is truthy but not a replacement config, thus return the default config.\n    return config;\n  }\n  // Return falsey.\n  return prop;\n}\n\n/**\n * Used for features that are enabled by default unless specified otherwise.\n *\n * @category utils\n */\nexport function overrideWithPropertyOrConfig(\n  prop: any,\n  config: boolean | { [key: string]: any },\n  merge: boolean = false\n): any {\n  if (prop === undefined) {\n    return config;\n  }\n  return enableWithPropertyOrConfig(prop, config, merge);\n}\n"]}