{"version": 3, "file": "escapeStringRegexp.js", "sourceRoot": "", "sources": ["../../src/utils/escapeStringRegexp.ts"], "names": [], "mappings": ";;;AAAA,SAAgB,kBAAkB,CAAC,MAAc;IAC/C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;KAC1C;IAED,kFAAkF;IAClF,6JAA6J;IAC7J,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9E,CAAC;AARD,gDAQC", "sourcesContent": ["export function escapeStringRegexp(string: string) {\n  if (typeof string !== 'string') {\n    throw new TypeError('Expected a string');\n  }\n\n  // Escape characters with special meaning either inside or outside character sets.\n  // Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n  return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d');\n}\n"]}