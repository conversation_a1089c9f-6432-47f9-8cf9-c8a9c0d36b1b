{"version": 3, "file": "normalizePaths.js", "sourceRoot": "", "sources": ["../../src/utils/normalizePaths.ts"], "names": [], "mappings": ";;AAAA;;;;;;GAMG;AACH,SAAwB,cAAc,CACpC,OAAY,EACZ,eAA0C;IAE1C,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC;KAChB;SAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QACtC,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;KACjC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACjC,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC;KACrE;SAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QACtC,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACvC,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,eAAe,CAAC,CAAC;SAC/D;QACD,OAAO,MAAM,CAAC;KACf;SAAM;QACL,OAAO,OAAO,CAAC;KAChB;AACH,CAAC;AArBD,iCAqBC", "sourcesContent": ["/**\n * Converts absolute paths to relative paths for testing purposes.\n *\n * @param initial\n * @param transformString\n * @internal\n */\nexport default function normalizePaths(\n  initial: any,\n  transformString: (value: string) => string\n): any {\n  if (initial == null) {\n    return initial;\n  } else if (initial instanceof RegExp) {\n    return initial;\n  } else if (typeof initial === 'string') {\n    return transformString(initial);\n  } else if (Array.isArray(initial)) {\n    return initial.map(value => normalizePaths(value, transformString));\n  } else if (typeof initial === 'object') {\n    const result: { [key: string]: any } = {};\n    for (const prop of Object.keys(initial)) {\n      result[prop] = normalizePaths(initial[prop], transformString);\n    }\n    return result;\n  } else {\n    return initial;\n  }\n}\n"]}