{"version": 3, "file": "search.js", "sourceRoot": "", "sources": ["../../src/utils/search.ts"], "names": [], "mappings": ";;;AAAA;;;GAGG;AACH,+BAAgC;AAoBhC;;;;GAIG;AACH,SAAgB,eAAe,CAAC,KAAoB;IAClD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACjC,KAAK;QACL,IAAI;KACL,CAAC,CAAC,CAAC;AACN,CAAC;AALD,0CAKC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,MAAqB;IAC5C,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;IAC3C,OAAO,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AACnD,CAAC;AAHD,4BAGC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,MAAqB;;IACtD,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;IAC3C,MAAM,YAAY,GAAG,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/D,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE;QACnC,MAAM,IAAI,GAAQ,QAAQ,CAAC,IAAI,CAAC;QAChC,IACE,IAAI,CAAC,GAAG;YACR,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ;YAC5B,CAAA,MAAA,MAAA,IAAI,CAAC,GAAG,CAAC,OAAO,0CAAE,MAAM,0CAAE,mBAAmB,MAAK,mBAAmB,EACrE;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAdD,gDAcC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,KAA8B;IAC9D,MAAM,MAAM,GAAkB,EAAE,CAAC;IAEjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,IAAI,KAAK,KAAK,EAAE;YAClB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnB;SACF;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAbD,8CAaC;AAED;;;;;GAKG;AACH,SAAgB,uBAAuB,CACrC,MAAqB,EACrB,KAAe;IAEf,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC/B,MAAM,aAAa,GAAkC,EAAE,CAAC;IACxD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;KACzF;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAVD,0DAUC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,MAAqB,EAAE,KAAe;IACvE,MAAM,KAAK,GAAG,uBAAuB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACrD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;AACvE,CAAC;AAHD,gDAGC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,SAAuC,EACvC,IAAY;IAEZ,IAAI,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAC7B,IAAI,IAAA,eAAQ,EAAC,SAAS,CAAC,EAAE;QACvB,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;SAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;KACnC;SAAM,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;QAC1C,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;KACjC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACnC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KAC3D;IACD,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;SAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACpB,QAAQ,GAAG,EAAE;YACX,KAAK,MAAM;gBACT,OAAO,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3C,KAAK,SAAS;gBACZ,OAAO,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3C,KAAK,SAAS;gBACZ,OAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC5C,KAAK,KAAK;gBACR,OAAQ,KAA4B,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACjF,KAAK,IAAI;gBACP,OAAQ,KAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YAChF,KAAK,KAAK;gBACR,OAAQ,KAA4B,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YAClF;gBACE,OAAO,IAAI,CAAC;SACf;IACH,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAlCD,oDAkCC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,EAAE,OAAO,GAAG,EAAE,EAAiB;IACxD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7D,CAAC;AAFD,gCAEC;AAED;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,MAAqB,EAAE,IAAY;IAClE,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,EAAc,EAAE,EAAE;QAC1D,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE;YAChC,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC;SACzC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAPD,4CAOC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,MAAkB;IAC9C,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC;AACpE,CAAC;AAFD,sCAEC", "sourcesContent": ["/**\n * Loader flattening inspired by:\n * https://github.com/preactjs/preact-cli-experiment/tree/7b80623/packages/cli-plugin-legacy-config\n */\nimport { isRegExp } from 'util';\nimport {\n  Configuration,\n  RuleSetCondition,\n  RuleSetRule,\n  RuleSetUse,\n  RuleSetUseItem,\n  WebpackPluginInstance,\n} from 'webpack';\n\ninterface RuleItem {\n  rule: RuleSetRule;\n  index: number;\n}\n\ninterface PluginItem {\n  plugin: WebpackPluginInstance;\n  index: number;\n}\n\n/**\n *\n * @param rules\n * @category utils\n */\nexport function getRulesAsItems(rules: RuleSetRule[]): RuleItem[] {\n  return rules.map((rule, index) => ({\n    index,\n    rule,\n  }));\n}\n\n/**\n *\n * @param config\n * @category utils\n */\nexport function getRules(config: Configuration): RuleItem[] {\n  const { rules = [] } = config.module || {};\n  return getRulesAsItems(getRulesFromRules(rules));\n}\n\n/**\n * Get the babel-loader rule created by `@expo/webpack-config/loaders`\n *\n * @param config\n * @category utils\n */\nexport function getExpoBabelLoader(config: Configuration): RuleSetRule | null {\n  const { rules = [] } = config.module || {};\n  const currentRules = getRulesAsItems(getRulesFromRules(rules));\n  for (const ruleItem of currentRules) {\n    const rule: any = ruleItem.rule;\n    if (\n      rule.use &&\n      typeof rule.use === 'object' &&\n      rule.use.options?.caller?.__dangerous_rule_id === 'expo-babel-loader'\n    ) {\n      return rule;\n    }\n  }\n  return null;\n}\n\n/**\n *\n * @param rules\n * @category utils\n */\nexport function getRulesFromRules(rules: (RuleSetRule | '...')[]): RuleSetRule[] {\n  const output: RuleSetRule[] = [];\n\n  for (const rule of rules) {\n    if (rule !== '...') {\n      if (rule.oneOf) {\n        output.push(...getRulesFromRules(rule.oneOf));\n      } else {\n        output.push(rule);\n      }\n    }\n  }\n  return output;\n}\n\n/**\n *\n * @param config\n * @param files\n * @category utils\n */\nexport function getRulesByMatchingFiles(\n  config: Configuration,\n  files: string[]\n): { [key: string]: RuleItem[] } {\n  const rules = getRules(config);\n  const selectedRules: { [key: string]: RuleItem[] } = {};\n  for (const file of files) {\n    selectedRules[file] = rules.filter(({ rule }) => conditionMatchesFile(rule.test, file));\n  }\n  return selectedRules;\n}\n\n/**\n *\n * @param config\n * @param files\n * @category utils\n */\nexport function rulesMatchAnyFiles(config: Configuration, files: string[]): boolean {\n  const rules = getRulesByMatchingFiles(config, files);\n  return Object.keys(rules).some(filename => !!rules[filename].length);\n}\n\n/**\n *\n * @param condition\n * @param file\n * @category utils\n */\nexport function conditionMatchesFile(\n  condition: RuleSetCondition | undefined,\n  file: string\n): boolean {\n  if (!condition) return false;\n  if (isRegExp(condition)) {\n    return condition.test(file);\n  } else if (typeof condition === 'string') {\n    return file.startsWith(condition);\n  } else if (typeof condition === 'function') {\n    return Boolean(condition(file));\n  } else if (Array.isArray(condition)) {\n    return condition.some(c => conditionMatchesFile(c, file));\n  }\n  return Object.entries(condition)\n    .map(([key, value]) => {\n      switch (key) {\n        case 'test':\n          return conditionMatchesFile(value, file);\n        case 'include':\n          return conditionMatchesFile(value, file);\n        case 'exclude':\n          return !conditionMatchesFile(value, file);\n        case 'and':\n          return (value as RuleSetCondition[]).every(c => conditionMatchesFile(c, file));\n        case 'or':\n          return (value as RuleSetCondition[]).some(c => conditionMatchesFile(c, file));\n        case 'not':\n          return (value as RuleSetCondition[]).every(c => !conditionMatchesFile(c, file));\n        default:\n          return true;\n      }\n    })\n    .every(b => b);\n}\n\n/**\n *\n * @param param0\n * @category utils\n */\nexport function getPlugins({ plugins = [] }: Configuration): PluginItem[] {\n  return plugins.map((plugin, index) => ({ index, plugin }));\n}\n\n/**\n *\n * @param config\n * @param name\n * @category utils\n */\nexport function getPluginsByName(config: Configuration, name: string): PluginItem[] {\n  return getPlugins(config).filter(({ plugin }: PluginItem) => {\n    if (plugin && plugin.constructor) {\n      return plugin.constructor.name === name;\n    }\n    return false;\n  });\n}\n\n/**\n *\n * @param loader\n * @category utils\n */\nexport function isRuleSetItem(loader: RuleSetUse): loader is RuleSetUseItem {\n  return typeof loader === 'string' || typeof loader === 'function';\n}\n"]}