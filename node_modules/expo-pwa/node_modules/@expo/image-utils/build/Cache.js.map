{"version": 3, "file": "Cache.js", "sourceRoot": "", "sources": ["../src/Cache.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,uCAA6F;AAC7F,+BAAqC;AAIrC,MAAM,cAAc,GAAG,mCAAmC,CAAC;AAE3D,MAAM,SAAS,GAA8B,EAAE,CAAC;AAEhD,kEAAkE;AAClE,SAAS,aAAa,CAAC,QAAgB;IACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAA,uBAAY,EAAC,QAAQ,CAAC,CAAC;IACjF,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpE,CAAC;AAED,0DAA0D;AAC1D,SAAgB,cAAc,CAAC,UAAkB,EAAE,UAAoB;IACrE,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7D,CAAC;AAHD,wCAGC;AAEM,KAAK,UAAU,gCAAgC,CACpD,WAAmB,EACnB,IAAY,EACZ,IAAkB;IAElB,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;IAChG,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE;QAC5B,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM,oBAAoB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC/E;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAXD,4EAWC;AAEM,KAAK,UAAU,oBAAoB,CACxC,WAAmB,EACnB,IAAY,EACZ,QAAgB;IAEhB,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,WAAW,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtE,MAAM,IAAA,oBAAS,EAAC,WAAW,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC;AACrB,CAAC;AARD,oDAQC;AAEM,KAAK,UAAU,sBAAsB,CAC1C,QAAgB,EAChB,QAAgB;IAEhB,IAAI;QACF,OAAO,MAAM,IAAA,mBAAQ,EAAC,IAAA,cAAO,EAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC/D;IAAC,MAAM;QACN,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AATD,wDASC;AAEM,KAAK,UAAU,eAAe,CACnC,QAAgB,EAChB,MAAc,EACd,QAAgB;IAEhB,IAAI;QACF,MAAM,IAAA,oBAAS,EAAC,IAAA,cAAO,EAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;KACjE;IAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACpB,OAAO,CAAC,IAAI,CAAC,yBAAyB,QAAQ,MAAM,OAAO,EAAE,CAAC,CAAC;KAChE;AACH,CAAC;AAVD,0CAUC;AAEM,KAAK,UAAU,sBAAsB,CAAC,WAAmB,EAAE,IAAY;IAC5E,0BAA0B;IAC1B,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;IAC5D,MAAM,IAAA,oBAAS,EAAC,WAAW,CAAC,CAAC;IAC7B,MAAM,aAAa,GAAG,IAAA,sBAAW,EAAC,WAAW,CAAC,CAAC;IAE/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACjC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9C,OAAO;KACR;IACD,MAAM,mBAAmB,GAAoB,EAAE,CAAC;IAChD,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;QACjC,sBAAsB;QACtB,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACzB,SAAS;SACV;QAED,SAAS;QACT,IAAI,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC,EAAE;YACzB,mBAAmB,CAAC,IAAI,CAAC,IAAA,iBAAM,EAAC,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC5D;KACF;IAED,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACzC,CAAC;AAxBD,wDAwBC", "sourcesContent": ["import crypto from 'crypto';\nimport { ensureDir, readdirSync, readFile, readFileSync, remove, writeFile } from 'fs-extra';\nimport { join, resolve } from 'path';\n\nimport { ImageOptions } from './Image.types';\n\nconst CACHE_LOCATION = '.expo/web/cache/production/images';\n\nconst cacheKeys: { [key: string]: string } = {};\n\n// Calculate SHA256 Checksum value of a file based on its contents\nfunction calculateHash(filePath: string): string {\n  const contents = filePath.startsWith('http') ? filePath : readFileSync(filePath);\n  return crypto.createHash('sha256').update(contents).digest('hex');\n}\n\n// Create a hash key for caching the images between builds\nexport function createCacheKey(fileSource: string, properties: string[]): string {\n  const hash = calculateHash(fileSource);\n  return [hash].concat(properties).filter(Boolean).join('-');\n}\n\nexport async function createCacheKeyWithDirectoryAsync(\n  projectRoot: string,\n  type: string,\n  icon: ImageOptions\n): Promise<string> {\n  const cacheKey = `${type}-${createCacheKey(icon.src, [icon.resizeMode, icon.backgroundColor])}`;\n  if (!(cacheKey in cacheKeys)) {\n    cacheKeys[cacheKey] = await ensureCacheDirectory(projectRoot, type, cacheKey);\n  }\n\n  return cacheKey;\n}\n\nexport async function ensureCacheDirectory(\n  projectRoot: string,\n  type: string,\n  cacheKey: string\n): Promise<string> {\n  const cacheFolder = join(projectRoot, CACHE_LOCATION, type, cacheKey);\n  await ensureDir(cacheFolder);\n  return cacheFolder;\n}\n\nexport async function getImageFromCacheAsync(\n  fileName: string,\n  cacheKey: string\n): Promise<null | Buffer> {\n  try {\n    return await readFile(resolve(cacheKeys[cacheKey], fileName));\n  } catch {\n    return null;\n  }\n}\n\nexport async function cacheImageAsync(\n  fileName: string,\n  buffer: Buffer,\n  cacheKey: string\n): Promise<void> {\n  try {\n    await writeFile(resolve(cacheKeys[cacheKey], fileName), buffer);\n  } catch ({ message }) {\n    console.warn(`Error caching image: \"${fileName}\". ${message}`);\n  }\n}\n\nexport async function clearUnusedCachesAsync(projectRoot: string, type: string): Promise<void> {\n  // Clean up any old caches\n  const cacheFolder = join(projectRoot, CACHE_LOCATION, type);\n  await ensureDir(cacheFolder);\n  const currentCaches = readdirSync(cacheFolder);\n\n  if (!Array.isArray(currentCaches)) {\n    console.warn('Failed to read the icon cache');\n    return;\n  }\n  const deleteCachePromises: Promise<void>[] = [];\n  for (const cache of currentCaches) {\n    // skip hidden folders\n    if (cache.startsWith('.')) {\n      continue;\n    }\n\n    // delete\n    if (!(cache in cacheKeys)) {\n      deleteCachePromises.push(remove(join(cacheFolder, cache)));\n    }\n  }\n\n  await Promise.all(deleteCachePromises);\n}\n"]}