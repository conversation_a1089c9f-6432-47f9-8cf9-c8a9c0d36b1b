{"version": 3, "file": "Image.js", "sourceRoot": "", "sources": ["../src/Image.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,gDAAwB;AAExB,+CAAiC;AACjC,qDAAuC;AACvC,2CAA6B;AAE7B,+BAA4B;AAC5B,6CAA+B;AAC/B,+CAAiC;AAEjC,MAAM,kBAAkB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;AAElF,IAAI,SAAS,GAAY,KAAK,CAAC;AAE/B,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IAC9D,MAAM,KAAK,GAAG,MAAM,aAAa,EAAE,CAAC;IACpC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,YAA0B;IACnD,MAAM,KAAK,GAAQ,MAAM,aAAa,EAAE,CAAC;IACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;IACpE,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,YAAY,GAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACpE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC3C,KAAK;YACL,MAAM;YACN,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,eAAe;SAC5B,CAAC,CAAC;QAEH,IAAI,YAAY,CAAC,kBAAkB,EAAE;YACnC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,IAAI,YAAY,CAAC,YAAY,EAAE;YAC7B,kGAAkG;YAClG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC9B;QAED,wBAAwB;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;KACzC;IACD,IAAI;QACF,IAAI,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC;aACtC,WAAW,EAAE;aACb,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;QAEzE,kEAAkE;QAClE,IAAI,eAAe,IAAI,eAAe,KAAK,aAAa,EAAE;YACxD,wCAAwC;YACxC,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC;gBAClC;oBACE,4BAA4B;oBAC5B,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,KAAK;4BACL,MAAM;4BACN,qBAAqB;4BACrB,QAAQ,EAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACjD,UAAU,EAAE,eAAe;yBAC5B;qBACF;oBACD,gGAAgG;oBAChG,KAAK,EAAE,WAAW;iBACnB;aACF,CAAC,CAAC;SACJ;aAAM,IAAI,YAAY,CAAC,kBAAkB,EAAE;YAC1C,WAAW,CAAC,OAAO,EAAE,CAAC;SACvB;QAED,IAAI,YAAY,CAAC,YAAY,EAAE;YAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CACtB,iCAAiC,KAAK,aAAa,MAAM;cACnD,YAAY,CAAC,YAAY,SAAS,YAAY,CAAC,YAAY;gBAE/D,eAAe,IAAI,eAAe,KAAK,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAC3E,YAAY,CACb,CAAC;YAEF,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;SAC5D;QAED,OAAO,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;KAC3C;IAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,kDAAkD,YAAY,CAAC,GAAG,MAAM,OAAO,EAAE,CAClF,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,aAAa;IAC1B,IAAI,KAAU,CAAC;IACf,IAAI,MAAM,KAAK,CAAC,gBAAgB,EAAE;QAAE,KAAK,GAAG,MAAM,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACjF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CAAC,YAAoD;IAC3E,OAAO,YAAY,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM;QAC/C,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE;QACzB,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;AACrD,CAAC;AAED,KAAK,UAAU,kCAAkC;IAC/C,0GAA0G;IAC1G,IAAI,SAAG,CAAC,sBAAsB,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE;QACjF,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,qMAAqM,CACtM,CACF,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,uBAAuB,CAAC,YAA0B;IAC/D,MAAM,IAAI,GAAG;QACX,GAAG,YAAY;QACf,GAAG,EAAE,MAAM,QAAQ,CAAC,wBAAwB,CAAC,YAAY,CAAC,GAAG,CAAC;KAC/D,CAAC;IAEF,qBAAqB;IACrB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;IAED,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,2CAA2C,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KACxE;IACD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAC1C,MAAM,IAAI,KAAK,CAAC,iDAAiD,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;KACtF;IAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACd,IAAI,CAAC,IAAI,GAAG,QAAQ,eAAe,CAAC,YAAY,CAAC,IAAI,cAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;KACpF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,kBAAkB,CACtC,OAAoD,EACpD,YAA0B;IAE1B,MAAM,IAAI,GAAG,MAAM,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAEzD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACtB,MAAM,kCAAkC,EAAE,CAAC;QAC3C,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAK,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;KAC9D;IAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,gCAAgC,CAC3D,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,SAAS,EACjB,IAAI,CACL,CAAC;IAEF,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAC;IACxB,IAAI,MAAM,GAAkB,MAAM,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAE/E,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,kCAAkC,EAAE,CAAC;QAC3C,MAAM,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;KACrD;IAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1B,CAAC;AA3BD,gDA2BC;AAEM,KAAK,UAAU,oBAAoB,CACxC,cAAsB,EACtB,QAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAE9B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC/D,OAAO,MAAM,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC;AAND,oDAMC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,oBAAoB,CAAC,EACzC,UAAU,EACV,UAAU,EACV,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,GAMN;IACC,MAAM,KAAK,GAAQ,MAAM,aAAa,EAAE,CAAC;IACzC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAChE,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EACxC,CAAC,EACD,CAAC,CACF,CAAC;QACF,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACpD;IACD,OAAO,MAAM,KAAK,CAAC,UAAU,CAAC;SAC3B,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;SACnD,QAAQ,EAAE,CAAC;AAChB,CAAC;AAvBD,oDAuBC", "sourcesContent": ["import chalk from 'chalk';\nimport mime from 'mime';\n\nimport * as Cache from './Cache';\nimport * as Download from './Download';\nimport * as Ico from './Ico';\nimport { ImageOptions } from './Image.types';\nimport { env } from './env';\nimport * as Jim<PERSON> from './jimp';\nimport * as <PERSON> from './sharp';\n\nconst supportedMimeTypes = ['image/png', 'image/jpeg', 'image/webp', 'image/gif'];\n\nlet hasWarned: boolean = false;\n\nasync function resizeImagesAsync(buffer: Buffer, sizes: number[]): Promise<Buffer[]> {\n  const sharp = await getSharpAsync();\n  if (!sharp) {\n    return Jimp.resizeBufferAsync(buffer, sizes);\n  }\n  return Sharp.resizeBufferAsync(buffer, sizes);\n}\n\nasync function resizeAsync(imageOptions: ImageOptions): Promise<Buffer> {\n  const sharp: any = await getSharpAsync();\n  const { width, height, backgroundColor, resizeMode } = imageOptions;\n  if (!sharp) {\n    const inputOptions: any = { input: imageOptions.src, quality: 100 };\n    const jimp = await Jimp.resize(inputOptions, {\n      width,\n      height,\n      fit: resizeMode,\n      background: backgroundColor,\n    });\n\n    if (imageOptions.removeTransparency) {\n      jimp.colorType(2);\n    }\n    if (imageOptions.borderRadius) {\n      // TODO: support setting border radius with Jimp. Currently only support making the image a circle\n      await Jimp.circleAsync(jimp);\n    }\n\n    // Convert to png buffer\n    return jimp.getBufferAsync('image/png');\n  }\n  try {\n    let sharpBuffer = sharp(imageOptions.src)\n      .ensureAlpha()\n      .resize(width, height, { fit: resizeMode, background: 'transparent' });\n\n    // Skip an extra step if the background is explicitly transparent.\n    if (backgroundColor && backgroundColor !== 'transparent') {\n      // Add the background color to the image\n      sharpBuffer = sharpBuffer.composite([\n        {\n          // create a background color\n          input: {\n            create: {\n              width,\n              height,\n              // allow alpha colors\n              channels: imageOptions.removeTransparency ? 3 : 4,\n              background: backgroundColor,\n            },\n          },\n          // dest-over makes the first image (input) appear on top of the created image (background color)\n          blend: 'dest-over',\n        },\n      ]);\n    } else if (imageOptions.removeTransparency) {\n      sharpBuffer.flatten();\n    }\n\n    if (imageOptions.borderRadius) {\n      const mask = Buffer.from(\n        `<svg><rect x=\"0\" y=\"0\" width=\"${width}\" height=\"${height}\"\n        rx=\"${imageOptions.borderRadius}\" ry=\"${imageOptions.borderRadius}\"\n        fill=\"${\n          backgroundColor && backgroundColor !== 'transparent' ? backgroundColor : 'none'\n        }\" /></svg>`\n      );\n\n      sharpBuffer.composite([{ input: mask, blend: 'dest-in' }]);\n    }\n\n    return await sharpBuffer.png().toBuffer();\n  } catch ({ message }) {\n    throw new Error(\n      `It was not possible to generate splash screen '${imageOptions.src}'. ${message}`\n    );\n  }\n}\n\nasync function getSharpAsync(): Promise<any> {\n  let sharp: any;\n  if (await Sharp.isAvailableAsync()) sharp = await Sharp.findSharpInstanceAsync();\n  return sharp;\n}\n\nfunction getDimensionsId(imageOptions: Pick<ImageOptions, 'width' | 'height'>): string {\n  return imageOptions.width === imageOptions.height\n    ? `${imageOptions.width}`\n    : `${imageOptions.width}x${imageOptions.height}`;\n}\n\nasync function maybeWarnAboutInstallingSharpAsync() {\n  // Putting the warning here will prevent the warning from showing if all images were reused from the cache\n  if (env.EXPO_IMAGE_UTILS_DEBUG && !hasWarned && !(await Sharp.isAvailableAsync())) {\n    hasWarned = true;\n    console.warn(\n      chalk.yellow(\n        `Using node to generate images. This is much slower than using native packages.\\n\\u203A Optionally you can stop the process and try again after successfully running \\`npm install -g sharp-cli\\`.\\n`\n      )\n    );\n  }\n}\n\nasync function ensureImageOptionsAsync(imageOptions: ImageOptions): Promise<ImageOptions> {\n  const icon = {\n    ...imageOptions,\n    src: await Download.downloadOrUseCachedImage(imageOptions.src),\n  };\n\n  // Default to contain\n  if (!icon.resizeMode) {\n    icon.resizeMode = 'contain';\n  }\n\n  const mimeType = mime.getType(icon.src);\n\n  if (!mimeType) {\n    throw new Error(`Invalid mimeType for image with source: ${icon.src}`);\n  }\n  if (!supportedMimeTypes.includes(mimeType)) {\n    throw new Error(`Supplied image is not a supported image type: ${imageOptions.src}`);\n  }\n\n  if (!icon.name) {\n    icon.name = `icon_${getDimensionsId(imageOptions)}.${mime.getExtension(mimeType)}`;\n  }\n\n  return icon;\n}\n\nexport async function generateImageAsync(\n  options: { projectRoot: string; cacheType?: string },\n  imageOptions: ImageOptions\n): Promise<{ source: Buffer; name: string }> {\n  const icon = await ensureImageOptionsAsync(imageOptions);\n\n  if (!options.cacheType) {\n    await maybeWarnAboutInstallingSharpAsync();\n    return { name: icon.name!, source: await resizeAsync(icon) };\n  }\n\n  const cacheKey = await Cache.createCacheKeyWithDirectoryAsync(\n    options.projectRoot,\n    options.cacheType,\n    icon\n  );\n\n  const name = icon.name!;\n  let source: Buffer | null = await Cache.getImageFromCacheAsync(name, cacheKey);\n\n  if (!source) {\n    await maybeWarnAboutInstallingSharpAsync();\n    source = await resizeAsync(icon);\n    await Cache.cacheImageAsync(name, source, cacheKey);\n  }\n\n  return { name, source };\n}\n\nexport async function generateFaviconAsync(\n  pngImageBuffer: Buffer,\n  sizes: number[] = [16, 32, 48]\n): Promise<Buffer> {\n  const buffers = await resizeImagesAsync(pngImageBuffer, sizes);\n  return await Ico.generateAsync(buffers);\n}\n\n/**\n * Layers the provided foreground image over the provided background image.\n *\n * @param foregroundImageBuffer\n * @param foregroundImageBuffer\n * @param x pixel offset from the left edge, defaults to 0.\n * @param y pixel offset from the top edge, defaults to 0.\n */\nexport async function compositeImagesAsync({\n  foreground,\n  background,\n  x = 0,\n  y = 0,\n}: {\n  foreground: Buffer;\n  background: Buffer;\n  x?: number;\n  y?: number;\n}): Promise<Buffer> {\n  const sharp: any = await getSharpAsync();\n  if (!sharp) {\n    const image = (await Jimp.getJimpImageAsync(background)).composite(\n      await Jimp.getJimpImageAsync(foreground),\n      x,\n      y\n    );\n    return await image.getBufferAsync(image.getMIME());\n  }\n  return await sharp(background)\n    .composite([{ input: foreground, left: x, top: y }])\n    .toBuffer();\n}\n"]}