{"version": 3, "file": "sharp.types.js", "sourceRoot": "", "sources": ["../src/sharp.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ImageFormat, ResizeMode } from './Image.types';\n\nexport type SharpGlobalOptions = {\n  compressionLevel?: '';\n  format?: ImageFormat;\n  input: string;\n  limitInputPixels?: number;\n  output: string;\n  progressive?: boolean;\n  quality?: number;\n  withMetadata?: boolean;\n  [key: string]: string | number | boolean | undefined | null;\n};\n\nexport type SharpCommandOptions = RemoveAlphaOptions | ResizeOptions | FlattenOptions;\n\nexport type FlattenOptions = {\n  operation: 'flatten';\n  background: string;\n};\n\nexport type RemoveAlphaOptions = {\n  operation: 'removeAlpha';\n};\n\nexport type Position =\n  | 'center'\n  | 'centre'\n  | 'north'\n  | 'east'\n  | 'south'\n  | 'west'\n  | 'northeast'\n  | 'southeast'\n  | 'southwest'\n  | 'northwest'\n  | 'top'\n  | 'right'\n  | 'bottom'\n  | 'left'\n  | 'right top'\n  | 'right bottom'\n  | 'left bottom'\n  | 'left top'\n  | 'entropy'\n  | 'attention';\n\nexport type ResizeOptions = {\n  operation: 'resize';\n  background?: string;\n  fastShrinkOnLoad?: boolean;\n  fit?: ResizeMode;\n  height?: number;\n  kernel?: 'nearest' | 'cubic' | 'mitchell' | 'lanczos2' | 'lanczos3';\n  position?: Position;\n  width: number;\n  withoutEnlargement?: boolean;\n};\n\nexport type Options =\n  | object\n  | {\n      [key: string]: boolean | number | string | undefined;\n    };\n"]}