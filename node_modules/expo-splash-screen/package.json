{"name": "expo-splash-screen", "version": "0.20.5", "description": "Provides a module to allow keeping the native Splash Screen visible until you choose to hide it.", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-splash-screen", "splash-screen", "splash", "launch-screen", "launch"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-splash-screen"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/splash-screen/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/prebuild-config": "6.2.6"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "0d774d352ca6d11f83a3223199c8e5f6ba4c8e09"}