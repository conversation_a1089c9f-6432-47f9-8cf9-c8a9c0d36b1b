# @react-native/normalize-colors

[![Version][version-badge]][package]

## Installation

```
yarn add --dev @react-native/normalize-colors
```

*Note: We're using `yarn` to install deps. Feel free to change commands to use `npm` 3+ and `npx` if you like*

[version-badge]: https://img.shields.io/npm/v/@react-native/normalize-colors?style=flat-square
[package]: https://www.npmjs.com/package/@react-native/normalize-colors

## Testing

To run the tests in this package, run the following commands from the React Native root folder:

1. `yarn` to install the dependencies. You just need to run this once
2. `yarn jest packages/normalize-color`.
