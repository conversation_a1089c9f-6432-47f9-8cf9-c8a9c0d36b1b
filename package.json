{"name": "logistics-service-provider-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@types/react": "~19.0.10", "expo": "~53.0.0", "expo-font": "~13.3.2", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.0", "typescript": "~5.8.3"}, "private": true}