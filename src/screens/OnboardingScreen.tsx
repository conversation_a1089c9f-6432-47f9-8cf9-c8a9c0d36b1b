"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, Dimensions } from "react-native"
import Icon from "../components/Icon"

interface OnboardingScreenProps {
  onComplete: () => void
}

const { width, height } = Dimensions.get('window')

const OnboardingScreen = ({ onComplete }: OnboardingScreenProps) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    fullName: "",
    mobileNumber: "",
    companyName: "",
    otpCode: ["", "", "", "", "", ""],
    documents: {
      panCard: null,
      gstCertificate: null,
      addressProof: null
    }
  })

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const updateOTP = (index: number, value: string) => {
    const newOTP = [...formData.otpCode]
    newOTP[index] = value
    setFormData(prev => ({ ...prev, otpCode: newOTP }))
  }

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      {[1, 2, 3, 4].map((step) => (
        <View key={step} style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressStep,
              currentStep >= step ? styles.progressStepActive : styles.progressStepInactive
           ]}
          >
            {currentStep > step ? (
              <Icon name="check" size={12} color="white" />
            ) : (
              <Text style={[
                styles.progressStepText,
                currentStep >= step ? styles.progressStepTextActive : styles.progressStepTextInactive
              ]}>
                {step}
              </Text>
            )}
          </View>
          {step < 4 && (
            <View 
              style={[
                styles.progressLine,
                currentStep > step ? styles.progressLineActive : styles.progressLineInactive
              ]} 
            />
          )}
        </View>
      ))}
    </View>
  )

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Welcome to Link LOGISTICS</Text>
      <Text style={styles.stepSubtitle}>Let's get started by verifying your details.</Text>
      
      <View style={styles.formContainer}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Full Name</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter your full name"
            placeholderTextColor="#999"
            value={formData.fullName}
            onChangeText={(text) => updateFormData('fullName', text)}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Mobile Number</Text>
          <TextInput
            style={styles.textInput}
            placeholder="+91 XXXXX XXXXX"
            placeholderTextColor="#999"
            keyboardType="phone-pad"
            value={formData.mobileNumber}
            onChangeText={(text) => updateFormData('mobileNumber', text)}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Company Name</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter your company name"
            placeholderTextColor="#999"
            value={formData.companyName}
            onChangeText={(text) => updateFormData('companyName', text)}
          />
        </View>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
        <Text style={styles.primaryButtonText}>Continue</Text>
        <Icon name="arrow-right" size={20} color="white" />
      </TouchableOpacity>

      <TouchableOpacity style={styles.linkButton}>
        <Text style={styles.linkButtonText}>Already have an account? Sign In</Text>
      </TouchableOpacity>
    </View>
  )

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Verify Your Email</Text>
      <Text style={styles.stepSubtitle}>We've sent a code to your email. Enter it below to continue.</Text>
      
      <View style={styles.otpContainer}>
        {formData.otpCode.map((digit, index) => (
          <TextInput
            key={index}
            style={styles.otpInput}
            value={digit}
            onChangeText={(text) => updateOTP(index, text)}
            keyboardType="numeric"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
        <Text style={styles.primaryButtonText}>Verify Email</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.linkButton}>
        <Text style={styles.linkButtonText}>Didn't receive code? Resend</Text>
      </TouchableOpacity>
    </View>
  )

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Upload Required Documents</Text>
      <Text style={styles.stepSubtitle}>Upload valid KYC/Business proof to proceed.</Text>
      
      <View style={styles.documentsContainer}>
        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.uploadIcon}>
            <Icon name="upload" size={24} color="#4A90E2" />
          </View>
          <Text style={styles.uploadTitle}>PAN Card</Text>
          <Text style={styles.uploadSubtitle}>Upload PAN Card copy</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.uploadIcon}>
            <Icon name="upload" size={24} color="#4A90E2" />
          </View>
          <Text style={styles.uploadTitle}>GST Certificate</Text>
          <Text style={styles.uploadSubtitle}>Upload GST Certificate</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.uploadIcon}>
            <Icon name="upload" size={24} color="#4A90E2" />
          </View>
          <Text style={styles.uploadTitle}>Address Proof</Text>
          <Text style={styles.uploadSubtitle}>Upload Address Proof</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
        <Text style={styles.primaryButtonText}>Submit Documents</Text>
      </TouchableOpacity>
    </View>
  )

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <View style={styles.successContainer}>
        <View style={styles.successIcon}>
          <Icon name="check" size={48} color="white" />
        </View>
        <Text style={styles.successTitle}>✅ You're All Set!</Text>
        <Text style={styles.successSubtitle}>Your login process is complete. Welcome aboard.</Text>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={onComplete}>
        <Text style={styles.primaryButtonText}>Go to Home Page</Text>
        <Icon name="arrow-right" size={20} color="white" />
      </TouchableOpacity>
    </View>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1()
      case 2: return renderStep2()
      case 3: return renderStep3()
      case 4: return renderStep4()
      default: return renderStep1()
    }
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {renderProgressBar()}
        {renderCurrentStep()}
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  scrollContainer: {
    flex: 1,
  },
  progressContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingTop: 60,
    paddingBottom: 40,
  },
  progressBarContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressStep: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  progressStepActive: {
    backgroundColor: "#4A90E2",
    shadowColor: "#4A90E2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  progressStepInactive: {
    backgroundColor: "#E2E8F0",
    borderWidth: 2,
    borderColor: "#CBD5E0",
  },
  progressStepText: {
    fontSize: 14,
    fontWeight: "600",
  },
  progressStepTextActive: {
    color: "white",
  },
  progressStepTextInactive: {
    color: "#94A3B8",
  },
  progressLine: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  progressLineActive: {
    backgroundColor: "#4A90E2",
  },
  progressLineInactive: {
    backgroundColor: "#E2E8F0",
  },
  stepContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1E293B",
    textAlign: "center",
    marginBottom: 12,
  },
  stepSubtitle: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    marginBottom: 40,
    lineHeight: 24,
  },
  formContainer: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#E2E8F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  otpInput: {
    width: 48,
    height: 56,
    backgroundColor: "white",
    borderRadius: 12,
    fontSize: 24,
    fontWeight: "bold",
    color: "#1E293B",
    borderWidth: 2,
    borderColor: "#E2E8F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  documentsContainer: {
    marginBottom: 32,
  },
  documentUpload: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#E2E8F0",
    borderStyle: "dashed",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  uploadIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#EBF4FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  uploadTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1E293B",
    marginBottom: 4,
  },
  uploadSubtitle: {
    fontSize: 14,
    color: "#64748B",
  },
  successContainer: {
    alignItems: "center",
    marginBottom: 48,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#10B981",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    shadowColor: "#10B981",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  successTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1E293B",
    textAlign: "center",
    marginBottom: 12,
  },
  successSubtitle: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    lineHeight: 24,
  },
  primaryButton: {
    backgroundColor: "#4A90E2",
    borderRadius: 16,
    padding: 18,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#4A90E2",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    marginBottom: 16,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
    marginRight: 8,
  },
  linkButton: {
    padding: 12,
    alignItems: "center",
  },
  linkButtonText: {
    fontSize: 14,
    color: "#4A90E2",
    fontWeight: "500",
  },
})

export default OnboardingScreen
