"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from "react-native"
import Icon from "../components/Icon"

interface OnboardingScreenProps {
  onComplete: () => void
}

const OnboardingScreen = ({ onComplete }: OnboardingScreenProps) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [isVerifying, setIsVerifying] = useState(false)
  const [formData, setFormData] = useState({
    fullName: "",
    mobileNumber: "",
    companyName: "",
    otpCode: ["", "", "", "", "", ""],
    documents: {
      panCard: null,
      gstCertificate: null,
      addressProof: null
    }
  })

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const updateOTP = (index: number, value: string) => {
    const newOTP = [...formData.otpCode]
    newOTP[index] = value
    setFormData(prev => ({ ...prev, otpCode: newOTP }))

    // Auto-focus next input if value is entered
    if (value && index < 5) {
      // Focus next input (you can implement this with refs if needed)
    }

    // Auto-continue when all 6 digits are entered
    const updatedOTP = [...newOTP]
    const isComplete = updatedOTP.every(digit => digit !== "")
    if (isComplete && currentStep === 2) {
      setIsVerifying(true)
      // Simulate verification process
      setTimeout(() => {
        setIsVerifying(false)
        nextStep()
      }, 1500) // 1.5 seconds for verification animation
    }
  }
``
  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    } else {
      onComplete()
    }
  }

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      {[1, 2, 3, 4].map((step) => (
        <View key={step} style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressStep,
              currentStep >= step ? styles.progressStepActive : styles.progressStepInactive
           ]}
          >
            {currentStep > step ? (
              <Icon name="check" size={12} color="white" />
            ) : (
              <Text style={[
                styles.progressStepText,
                currentStep >= step ? styles.progressStepTextActive : styles.progressStepTextInactive
              ]}>
                {step}
              </Text>
            )}
          </View>
          {step < 4 && (
            <View 
              style={[
                styles.progressLine,
                currentStep > step ? styles.progressLineActive : styles.progressLineInactive
              ]} 
            />
          )}
        </View>
      ))}
    </View>
  )

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}> Welcome to Link My Logistics </Text>
      <Text style={styles.stepSubtitle}>Let's get started by verifying your details.</Text>
      
      <View style={styles.formContainer}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Full Name</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter your full name"
            placeholderTextColor="#999"
            value={formData.fullName}
            onChangeText={(text) => updateFormData('fullName', text)}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Mobile Number</Text>
          <TextInput
            style={styles.textInput}
            placeholder="+91 XXXXX XXXXX"
            placeholderTextColor="#999"
            keyboardType="phone-pad"
            value={formData.mobileNumber}
            onChangeText={(text) => updateFormData('mobileNumber', text)}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Company Name</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter your company name"
            placeholderTextColor="#999"
            value={formData.companyName}
            onChangeText={(text) => updateFormData('companyName', text)}
          />
        </View>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
        <Text style={styles.primaryButtonText}>Continue</Text>
        <Icon name="arrowright" size={29} color="white" />
      </TouchableOpacity>

      <TouchableOpacity style={styles.linkButton}>
        <Text style={styles.linkButtonText}>Already have an account ? &nbsp;<Text style={[styles.linkButtonText, { color: 'red' }]}>Sign In</Text></Text>
      </TouchableOpacity>
    </View>
  )

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Verify Your Email</Text>
      <Text style={styles.stepSubtitle}>
        {isVerifying ? "Verifying your code..." : "We've sent a code to your email. Enter it below to continue."}
      </Text>
      
      <View style={styles.otpContainer}>
        {formData.otpCode.map((digit, index) => (
          <View key={index} style={styles.otpInputContainer}>
            <TextInput
              style={[
                styles.otpInput,
                digit ? styles.otpInputFilled : null,
                isVerifying ? styles.otpInputVerifying : null
              ]}
              value={digit}
              onChangeText={(text) => updateOTP(index, text)}
              keyboardType="numeric"
              maxLength={1}
              textAlign="center"
              editable={!isVerifying}
            />
            {digit && <View style={styles.otpInputActive} />}
            {isVerifying && <View style={styles.otpVerifyingPulse} />}
          </View>
        ))}
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, isVerifying ? styles.primaryButtonDisabled : null]}
        onPress={nextStep}
        disabled={isVerifying}
      >
        {isVerifying ? (
          <>
            <View style={styles.loadingSpinner} />
            <Text style={styles.primaryButtonText}>Verifying...</Text>
          </>
        ) : (
          <>
            <View style={styles.buttonIcon}>
            </View>
            <Text style={styles.primaryButtonText}>Verify & Continue</Text>
             <Icon name="shieldcheck" size={27} color="white" />
          </>
        )}
        <View style={styles.buttonGlow} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.linkButton}>
        <Text style={styles.linkButtonText}>Didn't receive code? &nbsp;<Text style={[styles.linkButton, { color: 'red' }]}>Re-send</Text></Text>
      </TouchableOpacity>
      
    </View>
  )

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Upload Required Documents</Text>
      <Text style={styles.stepSubtitle}>Secure document verification for your business account</Text>

      <View style={styles.documentsContainer}>
        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.documentHeader}>
            <View style={styles.uploadIconCircle}>
              <Icon name="card" size={24} color="#4A90E2" />
            </View>
            <View style={styles.documentInfo}>
              <Text style={styles.uploadTitle}>PAN Card</Text>
              <Text style={styles.uploadSubtitle}>Identity verification document</Text>
            </View>
            <View style={styles.uploadStatusBadge}>
              <Icon name="upload" size={16} color="#4A90E2" />
            </View>
          </View>
          <View style={styles.uploadProgress}>
            <View style={styles.progressBar} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.documentHeader}>
            <View style={styles.uploadIconCircle}>
              <Icon name="filetext" size={24} color="#4A90E2" />
            </View>
            <View style={styles.documentInfo}>
              <Text style={styles.uploadTitle}>GST Certificate</Text>
              <Text style={styles.uploadSubtitle}>Business registration proof</Text>
            </View>
            <View style={styles.uploadStatusBadge}>
              <Icon name="upload" size={16} color="#4A90E2" />
            </View>
          </View>
          <View style={styles.uploadProgress}>
            <View style={styles.progressBar} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.documentUpload}>
          <View style={styles.documentHeader}>
            <View style={styles.uploadIconCircle}>
              <Icon name="home" size={24} color="#4A90E2" />
            </View>
            <View style={styles.documentInfo}>
              <Text style={styles.uploadTitle}>Address Proof</Text>
              <Text style={styles.uploadSubtitle}>Business address verification</Text>
            </View>
            <View style={styles.uploadStatusBadge}>
              <Icon name="upload" size={16} color="#4A90E2" />
            </View>
          </View>
          <View style={styles.uploadProgress}>
            <View style={styles.progressBar} />
          </View>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
        <View style={styles.buttonIcon}>
        </View>
        <Text style={styles.primaryButtonText}>Submit Documents</Text>
        <View style={styles.buttonGlow} />
      </TouchableOpacity>
    </View>
  )

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <View style={styles.successContainer}>
        <View style={styles.successIcon}>
          <Icon name="check" size={48} color="white" />
        </View>
        <Text style={styles.successTitle}>✅ You're All Set!</Text>
        <Text style={styles.successSubtitle}>Your login process is complete. Welcome aboard.</Text>
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={onComplete}>
        <Text style={styles.primaryButtonText}>Go to Home Page</Text>
        <Icon name="arrowright" size={25} color="white" />
      </TouchableOpacity>
    </View>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1()
      case 2: return renderStep2()
      case 3: return renderStep3()
      case 4: return renderStep4()
      default: return renderStep1()
    }
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {renderProgressBar()}
        {renderCurrentStep()}
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  scrollContainer: {
    flex: 1,
  },
  progressContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingTop: 60,
    paddingBottom: 40,
  },
  progressBarContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressStep: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  progressStepActive: {
    backgroundColor: "#4A90E2",
    shadowColor: "#4A90E2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  progressStepInactive: {
    backgroundColor: "#E2E8F0",
    borderWidth: 2,
    borderColor: "#CBD5E0",
  },
  progressStepText: {
    fontSize: 14,
    fontWeight: "600",
  },
  progressStepTextActive: {
    color: "white",
  },
  progressStepTextInactive: {
    color: "#94A3B8",
  },
  progressLine: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  progressLineActive: {
    backgroundColor: "#4A90E2",
  },
  progressLineInactive: {
    backgroundColor: "#E2E8F0",
  },
  stepContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1E293B",
    textAlign: "center",
    marginBottom: 12,
  },
  stepSubtitle: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    marginBottom: 40,
    lineHeight: 24,
  },
  formContainer: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#E2E8F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  otpInput: {
    width: 48,
    height: 56,
    backgroundColor: "white",
    borderRadius: 12,
    fontSize: 24,
    fontWeight: "bold",
    color: "#1E293B",
    borderWidth: 2,
    borderColor: "#E2E8F0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  otpInputContainer: {
    position: "relative",
  },
  otpInputFilled: {
    borderColor: "#4A90E2",
    backgroundColor: "#EBF4FF",
  },
  otpInputVerifying: {
    borderColor: "#10B981",
    backgroundColor: "#ECFDF5",
  },
  otpInputActive: {
    position: "absolute",
    bottom: -2,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: "#4A90E2",
    borderRadius: 2,
  },
  otpVerifyingPulse: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 12,
    backgroundColor: "rgba(16, 185, 129, 0.1)",
    borderWidth: 2,
    borderColor: "rgba(16, 185, 129, 0.3)",
  },
  documentsContainer: {
    marginBottom: 32,
  },
  documentUpload: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#E2E8F0",
    borderStyle: "dashed",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  uploadIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#EBF4FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  uploadTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1E293B",
    marginBottom: 4,
  },
  uploadSubtitle: {
    fontSize: 14,
    color: "#64748B",
  },
  uploadIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#EBF4FF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#4A90E2",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  documentHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  documentInfo: {
    flex: 1,
    marginLeft: 12,
  },
  uploadStatusBadge: {
    backgroundColor: "#EBF4FF",
    borderRadius: 12,
    padding: 8,
  },
  uploadProgress: {
    height: 4,
    backgroundColor: "#F1F5F9",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    width: "0%",
    backgroundColor: "#4A90E2",
    borderRadius: 2,
  },
  successContainer: {
    alignItems: "center",
    marginBottom: 48,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#10B981",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    shadowColor: "#10B981",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  successTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1E293B",
    textAlign: "center",
    marginBottom: 12,
  },
  successSubtitle: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    lineHeight: 24,
  },
  primaryButton: {
    backgroundColor: "#4A90E2",
    borderRadius: 16,
    padding: 18,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#4A90E2",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    marginBottom: 16,
    position: "relative",
  },
  primaryButtonDisabled: {
    backgroundColor: "#94A3B8",
    shadowColor: "#94A3B8",
  },
  primaryButtonText: {
    fontSize: 20,
    fontWeight: "600",
    color: "white",
    marginRight: 8,
  },
  linkButton: {
    padding: 12,
    alignItems: "center",
  },
  linkButtonText: {
    fontSize: 14,
    color: "#4A90E2",
    fontWeight: "500",
    marginLeft: 6,
  },
  loadingSpinner: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "rgba(255,255,255,0.3)",
    borderTopColor: "white",
    marginRight: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonGlow: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    backgroundColor: "rgba(74, 144, 226, 0.1)",
  },
})

export default OnboardingScreen
